"""
数据访问器单元测试
"""

import unittest
from kafka_data_filter.data_accessor import DataAccessor


class TestDataAccessor(unittest.TestCase):
    """数据访问器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.accessor = DataAccessor()
        self.test_data = {
            'field1': 'value1',
            'field2': {
                'class2': 'nested_value',
                'subfield': {
                    'deep_field': 'deep_value'
                }
            },
            'field3': {
                'nature': '境外媒体'
            },
            'list_field': [
                {'item': 'item1'},
                {'item': 'item2'},
                'simple_item'
            ],
            'number_field': 42,
            'float_field': 3.14,
            'string_number': '123'
        }
    
    def test_get_field_value_simple(self):
        """测试获取简单字段值"""
        value = self.accessor.get_field_value(self.test_data, 'field1')
        self.assertEqual(value, 'value1')
    
    def test_get_field_value_nested(self):
        """测试获取嵌套字段值"""
        value = self.accessor.get_field_value(self.test_data, 'field2.class2')
        self.assertEqual(value, 'nested_value')
    
    def test_get_field_value_deep_nested(self):
        """测试获取深层嵌套字段值"""
        value = self.accessor.get_field_value(self.test_data, 'field2.subfield.deep_field')
        self.assertEqual(value, 'deep_value')
    
    def test_get_field_value_list_index(self):
        """测试获取列表索引值"""
        value = self.accessor.get_field_value(self.test_data, 'list_field.0.item')
        self.assertEqual(value, 'item1')
        
        value = self.accessor.get_field_value(self.test_data, 'list_field.2')
        self.assertEqual(value, 'simple_item')
    
    def test_get_field_value_nonexistent(self):
        """测试获取不存在的字段"""
        value = self.accessor.get_field_value(self.test_data, 'nonexistent')
        self.assertIsNone(value)
        
        value = self.accessor.get_field_value(self.test_data, 'field1.nonexistent')
        self.assertIsNone(value)
    
    def test_get_field_value_invalid_data(self):
        """测试无效数据类型"""
        value = self.accessor.get_field_value("not_a_dict", 'field1')
        self.assertIsNone(value)
        
        value = self.accessor.get_field_value(None, 'field1')
        self.assertIsNone(value)
    
    def test_get_field_value_empty_path(self):
        """测试空字段路径"""
        value = self.accessor.get_field_value(self.test_data, '')
        self.assertIsNone(value)
        
        value = self.accessor.get_field_value(self.test_data, None)
        self.assertIsNone(value)
    
    def test_field_exists_simple(self):
        """测试简单字段存在性"""
        self.assertTrue(self.accessor.field_exists(self.test_data, 'field1'))
        self.assertFalse(self.accessor.field_exists(self.test_data, 'nonexistent'))
    
    def test_field_exists_nested(self):
        """测试嵌套字段存在性"""
        self.assertTrue(self.accessor.field_exists(self.test_data, 'field2.class2'))
        self.assertFalse(self.accessor.field_exists(self.test_data, 'field2.nonexistent'))
    
    def test_field_exists_list_index(self):
        """测试列表索引存在性"""
        self.assertTrue(self.accessor.field_exists(self.test_data, 'list_field.0'))
        self.assertTrue(self.accessor.field_exists(self.test_data, 'list_field.2'))
        self.assertFalse(self.accessor.field_exists(self.test_data, 'list_field.10'))
    
    def test_get_field_type(self):
        """测试获取字段类型"""
        field_type = self.accessor.get_field_type(self.test_data, 'field1')
        self.assertEqual(field_type, str)
        
        field_type = self.accessor.get_field_type(self.test_data, 'number_field')
        self.assertEqual(field_type, int)
        
        field_type = self.accessor.get_field_type(self.test_data, 'float_field')
        self.assertEqual(field_type, float)
        
        field_type = self.accessor.get_field_type(self.test_data, 'field2')
        self.assertEqual(field_type, dict)
        
        field_type = self.accessor.get_field_type(self.test_data, 'list_field')
        self.assertEqual(field_type, list)
        
        field_type = self.accessor.get_field_type(self.test_data, 'nonexistent')
        self.assertIsNone(field_type)
    
    def test_is_field_numeric(self):
        """测试字段是否为数值类型"""
        self.assertTrue(self.accessor.is_field_numeric(self.test_data, 'number_field'))
        self.assertTrue(self.accessor.is_field_numeric(self.test_data, 'float_field'))
        self.assertFalse(self.accessor.is_field_numeric(self.test_data, 'field1'))
        self.assertFalse(self.accessor.is_field_numeric(self.test_data, 'nonexistent'))
    
    def test_is_field_string(self):
        """测试字段是否为字符串类型"""
        self.assertTrue(self.accessor.is_field_string(self.test_data, 'field1'))
        self.assertTrue(self.accessor.is_field_string(self.test_data, 'string_number'))
        self.assertFalse(self.accessor.is_field_string(self.test_data, 'number_field'))
        self.assertFalse(self.accessor.is_field_string(self.test_data, 'nonexistent'))
    
    def test_is_field_list(self):
        """测试字段是否为列表类型"""
        self.assertTrue(self.accessor.is_field_list(self.test_data, 'list_field'))
        self.assertFalse(self.accessor.is_field_list(self.test_data, 'field1'))
        self.assertFalse(self.accessor.is_field_list(self.test_data, 'nonexistent'))
    
    def test_convert_to_comparable(self):
        """测试值类型转换"""
        # 字符串数字转换
        result = self.accessor.convert_to_comparable('123')
        self.assertEqual(result, 123)
        self.assertIsInstance(result, int)
        
        result = self.accessor.convert_to_comparable('3.14')
        self.assertEqual(result, 3.14)
        self.assertIsInstance(result, float)
        
        # 非数字字符串保持不变
        result = self.accessor.convert_to_comparable('hello')
        self.assertEqual(result, 'hello')
        self.assertIsInstance(result, str)
        
        # 其他类型保持不变
        result = self.accessor.convert_to_comparable(42)
        self.assertEqual(result, 42)
        
        result = self.accessor.convert_to_comparable(None)
        self.assertIsNone(result)
    
    def test_get_field_value_with_dots_in_path(self):
        """测试字段路径中的点号处理"""
        # 测试连续点号
        value = self.accessor.get_field_value(self.test_data, 'field2..class2')
        self.assertEqual(value, 'nested_value')  # 应该忽略空字符串部分
        
        # 测试以点号开头和结尾的路径
        value = self.accessor.get_field_value(self.test_data, '.field1.')
        self.assertEqual(value, 'value1')


if __name__ == '__main__':
    unittest.main()
