"""
过滤规则解析器单元测试
"""

import unittest
from kafka_data_filter.filter_parser import FilterParser, TokenType, Token


class TestFilterParser(unittest.TestCase):
    """过滤规则解析器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.parser = FilterParser()
        self.test_data = {
            'field1': 1,
            'field2': {
                'class2': '新闻资讯'
            },
            'field3': {
                'nature': '境外媒体'
            },
            'score': 85,
            'title': '重要通知',
            'status': 'active',
            'tags': ['重要', '通知', '系统']
        }
    
    def test_tokenize_simple_expression(self):
        """测试简单表达式的词法分析"""
        expression = 'field1 = 1'
        tokens = self.parser.tokenize(expression)
        
        expected_types = [TokenType.FIELD, TokenType.OPERATOR, TokenType.VALUE, TokenType.EOF]
        actual_types = [token.type for token in tokens]
        
        self.assertEqual(actual_types, expected_types)
        self.assertEqual(tokens[0].value, 'field1')
        self.assertEqual(tokens[1].value, '=')
        self.assertEqual(tokens[2].value, '1')
    
    def test_tokenize_complex_expression(self):
        """测试复杂表达式的词法分析"""
        expression = 'field1 = 1 AND field2 > 10'
        tokens = self.parser.tokenize(expression)
        
        expected_values = ['field1', '=', '1', 'AND', 'field2', '>', '10']
        actual_values = [token.value for token in tokens[:-1]]  # 排除EOF
        
        self.assertEqual(actual_values, expected_values)
    
    def test_tokenize_string_values(self):
        """测试字符串值的词法分析"""
        expression = 'field1 = "test value"'
        tokens = self.parser.tokenize(expression)
        
        self.assertEqual(tokens[2].value, 'test value')
        self.assertEqual(tokens[2].type, TokenType.VALUE)
    
    def test_tokenize_in_operator(self):
        """测试IN运算符的词法分析"""
        expression = 'field1 IN ("value1", "value2")'
        tokens = self.parser.tokenize(expression)
        
        # 查找IN运算符
        in_token = next(token for token in tokens if token.value == 'IN')
        self.assertEqual(in_token.type, TokenType.OPERATOR)
    
    def test_tokenize_not_in_operator(self):
        """测试NOT IN运算符的词法分析"""
        expression = 'field1 NOT IN ("value1", "value2")'
        tokens = self.parser.tokenize(expression)
        
        # 查找NOT IN运算符
        not_in_token = next(token for token in tokens if token.value == 'NOT IN')
        self.assertEqual(not_in_token.type, TokenType.OPERATOR)
    
    def test_parse_simple_comparison(self):
        """测试简单比较表达式解析"""
        expression = 'field1 = 1'
        result = self.parser.parse(expression)
        
        expected = {
            'type': 'comparison',
            'field': 'field1',
            'operator': '=',
            'value': 1
        }
        
        self.assertEqual(result, expected)
    
    def test_parse_and_expression(self):
        """测试AND表达式解析"""
        expression = 'field1 = 1 AND field2 > 10'
        result = self.parser.parse(expression)
        
        self.assertEqual(result['type'], 'logical')
        self.assertEqual(result['operator'], 'AND')
        self.assertEqual(result['left']['field'], 'field1')
        self.assertEqual(result['right']['field'], 'field2')
    
    def test_parse_or_expression(self):
        """测试OR表达式解析"""
        expression = 'field1 = 1 OR field2 > 10'
        result = self.parser.parse(expression)
        
        self.assertEqual(result['type'], 'logical')
        self.assertEqual(result['operator'], 'OR')
    
    def test_parse_not_expression(self):
        """测试NOT表达式解析"""
        expression = 'NOT field1 = 1'
        result = self.parser.parse(expression)
        
        self.assertEqual(result['type'], 'logical')
        self.assertEqual(result['operator'], 'NOT')
        self.assertEqual(result['operand']['field'], 'field1')
    
    def test_parse_parentheses(self):
        """测试括号表达式解析"""
        expression = '(field1 = 1 OR field2 = 2) AND field3 = 3'
        result = self.parser.parse(expression)
        
        self.assertEqual(result['type'], 'logical')
        self.assertEqual(result['operator'], 'AND')
        self.assertEqual(result['left']['type'], 'logical')
        self.assertEqual(result['left']['operator'], 'OR')
    
    def test_parse_in_expression(self):
        """测试IN表达式解析"""
        expression = 'field1 IN ("value1", "value2", "value3")'
        result = self.parser.parse(expression)
        
        self.assertEqual(result['type'], 'comparison')
        self.assertEqual(result['operator'], 'IN')
        self.assertEqual(result['value'], ['value1', 'value2', 'value3'])
    
    def test_evaluate_simple_comparison_true(self):
        """测试简单比较求值 - 真"""
        expression_tree = {
            'type': 'comparison',
            'field': 'field1',
            'operator': '=',
            'value': 1
        }
        
        result = self.parser.evaluate(expression_tree, self.test_data)
        self.assertTrue(result)
    
    def test_evaluate_simple_comparison_false(self):
        """测试简单比较求值 - 假"""
        expression_tree = {
            'type': 'comparison',
            'field': 'field1',
            'operator': '=',
            'value': 2
        }
        
        result = self.parser.evaluate(expression_tree, self.test_data)
        self.assertFalse(result)
    
    def test_evaluate_and_expression_true(self):
        """测试AND表达式求值 - 真"""
        expression_tree = {
            'type': 'logical',
            'operator': 'AND',
            'left': {
                'type': 'comparison',
                'field': 'field1',
                'operator': '=',
                'value': 1
            },
            'right': {
                'type': 'comparison',
                'field': 'score',
                'operator': '>',
                'value': 80
            }
        }
        
        result = self.parser.evaluate(expression_tree, self.test_data)
        self.assertTrue(result)
    
    def test_evaluate_and_expression_false(self):
        """测试AND表达式求值 - 假"""
        expression_tree = {
            'type': 'logical',
            'operator': 'AND',
            'left': {
                'type': 'comparison',
                'field': 'field1',
                'operator': '=',
                'value': 1
            },
            'right': {
                'type': 'comparison',
                'field': 'score',
                'operator': '>',
                'value': 90
            }
        }
        
        result = self.parser.evaluate(expression_tree, self.test_data)
        self.assertFalse(result)
    
    def test_evaluate_or_expression_true(self):
        """测试OR表达式求值 - 真"""
        expression_tree = {
            'type': 'logical',
            'operator': 'OR',
            'left': {
                'type': 'comparison',
                'field': 'field1',
                'operator': '=',
                'value': 2  # 假
            },
            'right': {
                'type': 'comparison',
                'field': 'score',
                'operator': '>',
                'value': 80  # 真
            }
        }
        
        result = self.parser.evaluate(expression_tree, self.test_data)
        self.assertTrue(result)
    
    def test_evaluate_not_expression(self):
        """测试NOT表达式求值"""
        expression_tree = {
            'type': 'logical',
            'operator': 'NOT',
            'operand': {
                'type': 'comparison',
                'field': 'field1',
                'operator': '=',
                'value': 2
            }
        }
        
        result = self.parser.evaluate(expression_tree, self.test_data)
        self.assertTrue(result)  # field1 != 2，所以NOT为真
    
    def test_evaluate_in_expression_true(self):
        """测试IN表达式求值 - 真"""
        expression_tree = {
            'type': 'comparison',
            'field': 'field3.nature',
            'operator': 'IN',
            'value': ['境外媒体', '党媒']
        }
        
        result = self.parser.evaluate(expression_tree, self.test_data)
        self.assertTrue(result)
    
    def test_evaluate_in_expression_false(self):
        """测试IN表达式求值 - 假"""
        expression_tree = {
            'type': 'comparison',
            'field': 'field3.nature',
            'operator': 'IN',
            'value': ['国内媒体', '官方媒体']
        }
        
        result = self.parser.evaluate(expression_tree, self.test_data)
        self.assertFalse(result)
    
    def test_evaluate_not_in_expression(self):
        """测试NOT IN表达式求值"""
        expression_tree = {
            'type': 'comparison',
            'field': 'field2.class2',
            'operator': 'NOT IN',
            'value': ['广告信息', '股票资讯']
        }
        
        result = self.parser.evaluate(expression_tree, self.test_data)
        self.assertTrue(result)  # '新闻资讯' 不在列表中
    
    def test_evaluate_contains_expression_true(self):
        """测试CONTAINS表达式求值 - 真"""
        expression_tree = {
            'type': 'comparison',
            'field': 'title',
            'operator': 'CONTAINS',
            'value': '重要'
        }
        
        result = self.parser.evaluate(expression_tree, self.test_data)
        self.assertTrue(result)
    
    def test_evaluate_contains_expression_false(self):
        """测试CONTAINS表达式求值 - 假"""
        expression_tree = {
            'type': 'comparison',
            'field': 'title',
            'operator': 'CONTAINS',
            'value': '紧急'
        }
        
        result = self.parser.evaluate(expression_tree, self.test_data)
        self.assertFalse(result)
    
    def test_filter_data_integration(self):
        """测试完整的数据过滤功能"""
        expression = 'field1 = 1 AND field2.class2 NOT IN ("广告信息", "股票资讯") AND field3.nature IN ("境外媒体", "党媒")'
        
        result = self.parser.filter_data(expression, self.test_data)
        self.assertTrue(result)
    
    def test_comparison_operators(self):
        """测试各种比较运算符"""
        # 大于
        result = self.parser.filter_data('score > 80', self.test_data)
        self.assertTrue(result)
        
        # 小于
        result = self.parser.filter_data('score < 90', self.test_data)
        self.assertTrue(result)
        
        # 大于等于
        result = self.parser.filter_data('score >= 85', self.test_data)
        self.assertTrue(result)
        
        # 小于等于
        result = self.parser.filter_data('score <= 85', self.test_data)
        self.assertTrue(result)
    
    def test_invalid_expression(self):
        """测试无效表达式"""
        with self.assertRaises(ValueError):
            self.parser.parse('invalid expression without operator')
        
        with self.assertRaises(ValueError):
            self.parser.parse('field1 =')  # 缺少值
        
        with self.assertRaises(ValueError):
            self.parser.parse('= value')  # 缺少字段


if __name__ == '__main__':
    unittest.main()
