"""
配置管理器单元测试
"""

import unittest
import tempfile
import os
import yaml
from kafka_data_filter.config_manager import ConfigManager


class TestConfigManager(unittest.TestCase):
    """配置管理器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.test_config = {
            'kafka': {
                'bootstrap_servers': 'localhost:9092',
                'topic': 'test-topic',
                'group_id': 'test-group'
            },
            'filter': {
                'rules': ['field1 = 1', 'field2 > 10']
            },
            'output': {
                'type': 'console',
                'format': 'json'
            },
            'statistics': {
                'enabled': True,
                'interval': 10
            },
            'logging': {
                'level': 'INFO'
            }
        }
        
        # 创建临时配置文件
        self.temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False)
        yaml.dump(self.test_config, self.temp_file, default_flow_style=False, allow_unicode=True)
        self.temp_file.close()
        
    def tearDown(self):
        """测试后清理"""
        if os.path.exists(self.temp_file.name):
            os.unlink(self.temp_file.name)
    
    def test_load_config_success(self):
        """测试成功加载配置"""
        config_manager = ConfigManager(self.temp_file.name)
        
        # 验证配置加载
        self.assertEqual(config_manager.config, self.test_config)
    
    def test_load_config_file_not_found(self):
        """测试配置文件不存在"""
        with self.assertRaises(FileNotFoundError):
            ConfigManager('nonexistent.yaml')
    
    def test_get_kafka_config(self):
        """测试获取Kafka配置"""
        config_manager = ConfigManager(self.temp_file.name)
        kafka_config = config_manager.get_kafka_config()
        
        self.assertEqual(kafka_config, self.test_config['kafka'])
    
    def test_get_filter_rules(self):
        """测试获取过滤规则"""
        config_manager = ConfigManager(self.temp_file.name)
        rules = config_manager.get_filter_rules()
        
        self.assertEqual(rules, self.test_config['filter']['rules'])
    
    def test_get_output_config(self):
        """测试获取输出配置"""
        config_manager = ConfigManager(self.temp_file.name)
        output_config = config_manager.get_output_config()
        
        self.assertEqual(output_config, self.test_config['output'])
    
    def test_get_consumer_config(self):
        """测试获取消费者配置"""
        config_manager = ConfigManager(self.temp_file.name)
        consumer_config = config_manager.get_consumer_config()
        
        # 验证必要字段
        self.assertEqual(consumer_config['bootstrap_servers'], 'localhost:9092')
        self.assertEqual(consumer_config['group_id'], 'test-group')
        self.assertTrue('value_deserializer' in consumer_config)
    
    def test_get_topic(self):
        """测试获取topic"""
        config_manager = ConfigManager(self.temp_file.name)
        topic = config_manager.get_topic()
        
        self.assertEqual(topic, 'test-topic')
    
    def test_get_log_level(self):
        """测试获取日志级别"""
        config_manager = ConfigManager(self.temp_file.name)
        log_level = config_manager.get_log_level()
        
        self.assertEqual(log_level, 'INFO')
    
    def test_get_statistics_config(self):
        """测试获取统计配置"""
        config_manager = ConfigManager(self.temp_file.name)
        stats_config = config_manager.get_statistics_config()
        
        self.assertEqual(stats_config, self.test_config['statistics'])
    
    def test_validate_config_missing_section(self):
        """测试配置验证 - 缺少必要部分"""
        invalid_config = {'kafka': {'bootstrap_servers': 'localhost:9092'}}
        
        # 创建无效配置文件
        temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False)
        yaml.dump(invalid_config, temp_file, default_flow_style=False)
        temp_file.close()
        
        try:
            with self.assertRaises(ValueError):
                ConfigManager(temp_file.name)
        finally:
            os.unlink(temp_file.name)
    
    def test_validate_config_missing_kafka_field(self):
        """测试配置验证 - 缺少Kafka必要字段"""
        invalid_config = {
            'kafka': {'bootstrap_servers': 'localhost:9092'},  # 缺少topic
            'filter': {'rules': []},
            'output': {'type': 'console'}
        }
        
        temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False)
        yaml.dump(invalid_config, temp_file, default_flow_style=False)
        temp_file.close()
        
        try:
            with self.assertRaises(ValueError):
                ConfigManager(temp_file.name)
        finally:
            os.unlink(temp_file.name)
    
    def test_reload_config(self):
        """测试重新加载配置"""
        config_manager = ConfigManager(self.temp_file.name)
        
        # 修改配置文件
        new_config = self.test_config.copy()
        new_config['kafka']['topic'] = 'new-topic'
        
        with open(self.temp_file.name, 'w', encoding='utf-8') as f:
            yaml.dump(new_config, f, default_flow_style=False, allow_unicode=True)
        
        # 重新加载
        config_manager.reload_config()
        
        # 验证配置已更新
        self.assertEqual(config_manager.get_topic(), 'new-topic')


if __name__ == '__main__':
    unittest.main()
