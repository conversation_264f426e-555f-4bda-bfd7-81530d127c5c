#!/usr/bin/env python3
"""
清理旧文件脚本

移除重构前的旧文件，保持项目目录整洁
"""

import os
import shutil
import sys


def remove_file_or_dir(path):
    """安全地移除文件或目录"""
    try:
        if os.path.isfile(path):
            os.remove(path)
            print(f"✅ 已删除文件: {path}")
        elif os.path.isdir(path):
            shutil.rmtree(path)
            print(f"✅ 已删除目录: {path}")
        else:
            print(f"⚠️  路径不存在: {path}")
    except Exception as e:
        print(f"❌ 删除失败 {path}: {e}")


def main():
    """主函数"""
    print("=" * 60)
    print("清理重构前的旧文件")
    print("=" * 60)
    
    # 确认操作
    response = input("确定要删除旧文件吗？这个操作不可逆！(y/N): ")
    if response.lower() != 'y':
        print("操作已取消")
        return
    
    # 要删除的旧文件和目录列表
    old_files_and_dirs = [
        # 旧的项目文件
        'kafka_consumer_monitor',
        'kafka_data_filter', 
        'main.py',
        'kafka_filter_main.py',
        'config.example.yaml',
        'debug_describe_groups.py',
        'example_config.yaml',
        'filter_config.yaml',
        'reports',
        'requirements.txt',
        'run_tests.py',
        'test_filter_rules.py',
        'test_run_methods.py',
        'tests',
        
        # 旧的文档文件
        'README.md',
        'KAFKA_FILTER_README.md',
        'PROJECT_SUMMARY.md',
    ]
    
    print("\n开始清理旧文件...")
    
    for item in old_files_and_dirs:
        remove_file_or_dir(item)
    
    # 重命名新的README文件
    if os.path.exists('README_NEW.md'):
        try:
            os.rename('README_NEW.md', 'README.md')
            print("✅ 已重命名 README_NEW.md -> README.md")
        except Exception as e:
            print(f"❌ 重命名README文件失败: {e}")
    
    print("\n" + "=" * 60)
    print("清理完成！")
    print("=" * 60)
    print("\n当前项目结构:")
    print("├── kafka-consumer-monitor/    # Kafka消费者组监控程序")
    print("├── kafka-data-filter/         # Kafka数据过滤程序")
    print("├── docs/                      # 共享文档")
    print("├── README.md                  # 项目总览")
    print("└── PROJECT_RESTRUCTURE_PLAN.md # 重构计划文档")
    
    print("\n✅ 项目重构完成！两个项目现在完全独立。")
    print("\n快速开始:")
    print("  cd kafka-consumer-monitor && python main.py --help")
    print("  cd kafka-data-filter && python main.py --help")


if __name__ == "__main__":
    main()
