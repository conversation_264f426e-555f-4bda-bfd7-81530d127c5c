"""
文件报告生成器

提供监控报告的文件输出功能，支持HTML和Markdown格式
"""

import os
import json
import logging
import threading
from pathlib import Path
from typing import Dict, List, Optional
from datetime import datetime, timedelta
from jinja2 import Template

from ..models.event import Event
from ..utils.formatter import (
    format_timestamp, format_number, format_duration, format_percentage
)

logger = logging.getLogger(__name__)


class FileReporter:
    """
    文件报告生成器
    
    负责生成监控报告文件，支持HTML和Markdown格式
    """
    
    def __init__(self, config: Dict):
        """
        初始化文件报告生成器
        
        参数:
            config: 文件输出配置
        """
        self.config = config
        self.enabled = config.get('enabled', True)
        self.output_dir = Path(config.get('output_dir', './reports'))
        
        # 报告配置
        reports_config = config.get('reports', {})
        self.reports_enabled = reports_config.get('enabled', True)
        self.report_format = reports_config.get('format', 'html')
        self.report_interval = reports_config.get('interval', 3600)
        self.filename_template = reports_config.get('filename_template', 
                                                   'monitor_report_{timestamp}.{format}')
        
        # 事件日志配置
        event_log_config = config.get('event_log', {})
        self.event_log_enabled = event_log_config.get('enabled', True)
        self.event_log_filename = event_log_config.get('filename', 'events.log')
        
        self.current_groups = {}
        self.monitoring_stats = {}
        self.events_buffer = []
        
        self.report_thread = None
        self.is_running = False
        self.lock = threading.Lock()
        
        # 确保输出目录存在
        if self.enabled:
            self.output_dir.mkdir(parents=True, exist_ok=True)
    
    def start(self):
        """
        启动文件报告生成
        """
        if not self.enabled or not self.reports_enabled or self.is_running:
            return
        
        self.is_running = True
        self.report_thread = threading.Thread(target=self._report_loop, daemon=True)
        self.report_thread.start()
        logger.info("文件报告生成器已启动")
    
    def stop(self):
        """
        停止文件报告生成
        """
        self.is_running = False
        if self.report_thread and self.report_thread.is_alive():
            self.report_thread.join(timeout=5)
        logger.info("文件报告生成器已停止")
    
    def update_groups(self, groups_data: Dict[str, Dict]):
        """
        更新消费者组数据
        
        参数:
            groups_data: 消费者组数据
        """
        with self.lock:
            self.current_groups = groups_data.copy()
    
    def update_stats(self, stats: Dict):
        """
        更新监控统计信息
        
        参数:
            stats: 统计信息
        """
        with self.lock:
            self.monitoring_stats = stats.copy()
    
    def log_event(self, event: Event):
        """
        记录事件到日志文件
        
        参数:
            event: 事件对象
        """
        if not self.enabled or not self.event_log_enabled:
            return
        
        try:
            log_file = self.output_dir / self.event_log_filename
            
            # 将事件转换为JSON格式
            event_data = event.to_dict()
            event_line = json.dumps(event_data, ensure_ascii=False, separators=(',', ':'))
            
            # 追加到日志文件
            with open(log_file, 'a', encoding='utf-8') as f:
                f.write(event_line + '\n')
            
            # 添加到缓冲区用于报告生成
            with self.lock:
                self.events_buffer.append(event)
                # 限制缓冲区大小
                if len(self.events_buffer) > 1000:
                    self.events_buffer = self.events_buffer[-1000:]
        
        except Exception as e:
            logger.error(f"记录事件到文件失败: {e}")
    
    def _report_loop(self):
        """
        报告生成循环
        """
        last_report_time = datetime.now()
        
        while self.is_running:
            try:
                current_time = datetime.now()
                
                # 检查是否需要生成报告
                if (current_time - last_report_time).total_seconds() >= self.report_interval:
                    self._generate_report()
                    last_report_time = current_time
                
                # 休眠一段时间
                threading.Event().wait(60)  # 每分钟检查一次
                
            except Exception as e:
                logger.error(f"报告生成循环错误: {e}")
                threading.Event().wait(60)
    
    def _generate_report(self):
        """
        生成监控报告
        """
        try:
            with self.lock:
                groups_data = self.current_groups.copy()
                stats = self.monitoring_stats.copy()
                events = self.events_buffer.copy()
            
            # 生成报告数据
            report_data = self._prepare_report_data(groups_data, stats, events)
            
            # 根据格式生成报告
            if self.report_format.lower() == 'html':
                self._generate_html_report(report_data)
            elif self.report_format.lower() == 'markdown':
                self._generate_markdown_report(report_data)
            else:
                logger.warning(f"不支持的报告格式: {self.report_format}")
            
            logger.info(f"监控报告已生成: {self.report_format}")
            
        except Exception as e:
            logger.error(f"生成监控报告失败: {e}")
    
    def _prepare_report_data(self, groups_data: Dict, stats: Dict, events: List[Event]) -> Dict:
        """
        准备报告数据
        
        参数:
            groups_data: 消费者组数据
            stats: 监控统计
            events: 事件列表
            
        返回值:
            Dict: 报告数据
        """
        now = datetime.now()
        
        # 基本信息
        report_data = {
            'generated_at': now,
            'report_period': '1小时',
            'monitoring_stats': stats,
            'summary': {
                'total_groups': len(groups_data),
                'stable_groups': 0,
                'rebalancing_groups': 0,
                'total_lag': 0,
                'total_partitions': 0,
                'total_members': 0
            },
            'groups': [],
            'events': [],
            'alerts': []
        }
        
        # 统计摘要
        for group_info in groups_data.values():
            consumer_group = group_info['consumer_group']
            lag_stats = group_info['lag_stats']
            
            if consumer_group.is_stable:
                report_data['summary']['stable_groups'] += 1
            if consumer_group.is_rebalancing:
                report_data['summary']['rebalancing_groups'] += 1
            
            report_data['summary']['total_lag'] += lag_stats['total_lag']
            report_data['summary']['total_partitions'] += lag_stats['total_partitions']
            report_data['summary']['total_members'] += consumer_group.member_count
        
        # 消费者组详情
        for group_id, group_info in groups_data.items():
            consumer_group = group_info['consumer_group']
            lag_stats = group_info['lag_stats']
            
            group_data = {
                'group_id': group_id,
                'state': consumer_group.state.value,
                'member_count': consumer_group.member_count,
                'topics': consumer_group.topics,
                'total_lag': lag_stats['total_lag'],
                'max_partition_lag': lag_stats['max_partition_lag'],
                'lagging_partitions': lag_stats['lagging_partitions_count'],
                'total_partitions': lag_stats['total_partitions'],
                'last_updated': consumer_group.last_updated,
                'lag_by_topic': lag_stats.get('lag_by_topic', {}),
                'top_lagging_partitions': self._get_top_lagging_partitions(consumer_group, 5)
            }
            report_data['groups'].append(group_data)
        
        # 按积压排序
        report_data['groups'].sort(key=lambda g: g['total_lag'], reverse=True)
        
        # 最近事件（过去1小时）
        cutoff_time = now - timedelta(hours=1)
        recent_events = [e for e in events if e.timestamp > cutoff_time]
        recent_events.sort(key=lambda e: e.timestamp, reverse=True)
        
        for event in recent_events[:50]:  # 最多50个事件
            report_data['events'].append(event.to_dict())
        
        # 告警事件
        alert_events = [e for e in recent_events if e.severity.value in ['warning', 'error', 'critical']]
        for event in alert_events[:20]:  # 最多20个告警
            report_data['alerts'].append(event.to_dict())
        
        return report_data
    
    def _get_top_lagging_partitions(self, consumer_group, top_n: int) -> List[Dict]:
        """
        获取积压最多的分区
        
        参数:
            consumer_group: 消费者组对象
            top_n: 返回前N个
            
        返回值:
            List[Dict]: 分区列表
        """
        lagging_partitions = [p for p in consumer_group.partitions if p.lag and p.lag > 0]
        lagging_partitions.sort(key=lambda p: p.lag, reverse=True)
        
        result = []
        for i, partition in enumerate(lagging_partitions[:top_n]):
            result.append({
                'rank': i + 1,
                'topic': partition.topic,
                'partition': partition.partition,
                'lag': partition.lag,
                'current_offset': partition.current_offset,
                'latest_offset': partition.latest_offset,
                'consumer_id': partition.consumer_id
            })
        
        return result
    
    def _generate_html_report(self, report_data: Dict):
        """
        生成HTML格式报告
        
        参数:
            report_data: 报告数据
        """
        html_template = self._get_html_template()
        template = Template(html_template)
        
        # 渲染模板
        html_content = template.render(
            report=report_data,
            format_timestamp=format_timestamp,
            format_number=format_number,
            format_duration=format_duration,
            format_percentage=format_percentage
        )
        
        # 生成文件名
        timestamp = report_data['generated_at'].strftime('%Y%m%d_%H%M%S')
        filename = self.filename_template.format(timestamp=timestamp, format='html')
        
        # 写入文件
        report_file = self.output_dir / filename
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
    
    def _generate_markdown_report(self, report_data: Dict):
        """
        生成Markdown格式报告
        
        参数:
            report_data: 报告数据
        """
        md_template = self._get_markdown_template()
        template = Template(md_template)
        
        # 渲染模板
        md_content = template.render(
            report=report_data,
            format_timestamp=format_timestamp,
            format_number=format_number,
            format_duration=format_duration,
            format_percentage=format_percentage
        )
        
        # 生成文件名
        timestamp = report_data['generated_at'].strftime('%Y%m%d_%H%M%S')
        filename = self.filename_template.format(timestamp=timestamp, format='md')
        
        # 写入文件
        report_file = self.output_dir / filename
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(md_content)

    def _get_html_template(self) -> str:
        """
        获取HTML报告模板

        返回值:
            str: HTML模板字符串
        """
        return '''
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kafka消费者组监控报告</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        h1, h2, h3 { color: #333; }
        .header { text-align: center; border-bottom: 2px solid #007bff; padding-bottom: 20px; margin-bottom: 30px; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .summary-card { background: #f8f9fa; padding: 15px; border-radius: 5px; text-align: center; }
        .summary-card h3 { margin: 0 0 10px 0; color: #007bff; }
        .summary-card .value { font-size: 24px; font-weight: bold; color: #333; }
        table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #007bff; color: white; }
        tr:hover { background-color: #f5f5f5; }
        .status-stable { color: #28a745; font-weight: bold; }
        .status-rebalancing { color: #ffc107; font-weight: bold; }
        .status-dead { color: #dc3545; font-weight: bold; }
        .lag-high { color: #dc3545; font-weight: bold; }
        .lag-medium { color: #ffc107; font-weight: bold; }
        .lag-low { color: #28a745; }
        .event-info { color: #17a2b8; }
        .event-warning { color: #ffc107; }
        .event-error { color: #dc3545; }
        .event-critical { color: #dc3545; font-weight: bold; }
        .footer { text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Kafka消费者组监控报告</h1>
            <p>生成时间: {{ format_timestamp(report.generated_at) }}</p>
            <p>报告周期: {{ report.report_period }}</p>
        </div>

        <div class="summary">
            <div class="summary-card">
                <h3>消费者组总数</h3>
                <div class="value">{{ report.summary.total_groups }}</div>
            </div>
            <div class="summary-card">
                <h3>稳定状态</h3>
                <div class="value">{{ report.summary.stable_groups }}</div>
            </div>
            <div class="summary-card">
                <h3>重平衡中</h3>
                <div class="value">{{ report.summary.rebalancing_groups }}</div>
            </div>
            <div class="summary-card">
                <h3>总积压</h3>
                <div class="value">{{ format_number(report.summary.total_lag) }}</div>
            </div>
            <div class="summary-card">
                <h3>总分区数</h3>
                <div class="value">{{ report.summary.total_partitions }}</div>
            </div>
            <div class="summary-card">
                <h3>总消费者数</h3>
                <div class="value">{{ report.summary.total_members }}</div>
            </div>
        </div>

        <h2>消费者组详情</h2>
        <table>
            <thead>
                <tr>
                    <th>消费者组</th>
                    <th>状态</th>
                    <th>成员数</th>
                    <th>主题数</th>
                    <th>总积压</th>
                    <th>有积压分区</th>
                    <th>最后更新</th>
                </tr>
            </thead>
            <tbody>
                {% for group in report.groups %}
                <tr>
                    <td>{{ group.group_id }}</td>
                    <td class="{% if group.state == 'Stable' %}status-stable{% elif 'Rebalance' in group.state %}status-rebalancing{% else %}status-dead{% endif %}">
                        {{ group.state }}
                    </td>
                    <td>{{ group.member_count }}</td>
                    <td>{{ group.topics|length }}</td>
                    <td class="{% if group.total_lag > 10000 %}lag-high{% elif group.total_lag > 1000 %}lag-medium{% else %}lag-low{% endif %}">
                        {{ format_number(group.total_lag) }}
                    </td>
                    <td>{{ group.lagging_partitions }}/{{ group.total_partitions }}</td>
                    <td>{{ format_timestamp(group.last_updated, relative=True) }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>

        {% if report.alerts %}
        <h2>告警事件</h2>
        <table>
            <thead>
                <tr>
                    <th>时间</th>
                    <th>消费者组</th>
                    <th>事件类型</th>
                    <th>严重程度</th>
                    <th>消息</th>
                </tr>
            </thead>
            <tbody>
                {% for event in report.alerts %}
                <tr>
                    <td>{{ format_timestamp(event.timestamp) }}</td>
                    <td>{{ event.group_id }}</td>
                    <td>{{ event.event_type }}</td>
                    <td class="event-{{ event.severity }}">{{ event.severity.upper() }}</td>
                    <td>{{ event.message }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
        {% endif %}

        <div class="footer">
            <p>Kafka消费者组监控器 - 自动生成报告</p>
        </div>
    </div>
</body>
</html>
        '''

    def _get_markdown_template(self) -> str:
        """
        获取Markdown报告模板

        返回值:
            str: Markdown模板字符串
        """
        return '''
# Kafka消费者组监控报告

**生成时间:** {{ format_timestamp(report.generated_at) }}
**报告周期:** {{ report.report_period }}

## 监控摘要

| 指标 | 数值 |
|------|------|
| 消费者组总数 | {{ report.summary.total_groups }} |
| 稳定状态 | {{ report.summary.stable_groups }} |
| 重平衡中 | {{ report.summary.rebalancing_groups }} |
| 总积压 | {{ format_number(report.summary.total_lag) }} |
| 总分区数 | {{ report.summary.total_partitions }} |
| 总消费者数 | {{ report.summary.total_members }} |

## 消费者组详情

| 消费者组 | 状态 | 成员数 | 主题数 | 总积压 | 有积压分区 | 最后更新 |
|----------|------|--------|--------|--------|------------|----------|
{% for group in report.groups -%}
| {{ group.group_id }} | {{ group.state }} | {{ group.member_count }} | {{ group.topics|length }} | {{ format_number(group.total_lag) }} | {{ group.lagging_partitions }}/{{ group.total_partitions }} | {{ format_timestamp(group.last_updated, relative=True) }} |
{% endfor %}

{% if report.alerts %}
## 告警事件

| 时间 | 消费者组 | 事件类型 | 严重程度 | 消息 |
|------|----------|----------|----------|------|
{% for event in report.alerts -%}
| {{ format_timestamp(event.timestamp) }} | {{ event.group_id }} | {{ event.event_type }} | {{ event.severity.upper() }} | {{ event.message }} |
{% endfor %}
{% endif %}

{% if report.events %}
## 最近事件

| 时间 | 消费者组 | 事件类型 | 严重程度 | 消息 |
|------|----------|----------|----------|------|
{% for event in report.events[:20] -%}
| {{ format_timestamp(event.timestamp) }} | {{ event.group_id }} | {{ event.event_type }} | {{ event.severity.upper() }} | {{ event.message }} |
{% endfor %}
{% endif %}

---
*Kafka消费者组监控器 - 自动生成报告*
        '''
