# Kafka消费者组监控程序默认配置文件

# Kafka集群连接配置
kafka:
  # Kafka集群地址列表
  bootstrap_servers:
    - "**************:9092"
  
  # 安全配置（可选）
  security_protocol: "PLAINTEXT"  # PLAINTEXT, SSL, SASL_PLAINTEXT, SASL_SSL
  sasl_mechanism: null            # PLAIN, SCRAM-SHA-256, SCRAM-SHA-512, GSSAPI
  sasl_username: null
  sasl_password: null
  
  # SSL配置（当security_protocol为SSL或SASL_SSL时使用）
  ssl_cafile: null
  ssl_certfile: null
  ssl_keyfile: null
  ssl_password: null
  ssl_check_hostname: true
  
  # 连接超时配置
  request_timeout_ms: 30000       # 请求超时时间（毫秒）
  connections_max_idle_ms: 540000 # 连接最大空闲时间（毫秒）

# 监控配置
monitoring:
  # 监控间隔（秒）
  interval: 30
  
  # 要监控的消费者组列表（为空则监控所有）
  consumer_groups: []
  
  # 要排除的消费者组列表
  exclude_groups: []
  
  # 监控的主题列表（为空则监控所有）
  topics: []
  
  # 要排除的主题列表
  exclude_topics: []

# 告警配置
alerts:
  # 是否启用告警
  enabled: true
  
  # 积压告警阈值
  lag_threshold: 1000
  
  # 重平衡持续时间告警阈值（秒）
  rebalance_duration_threshold: 60
  
  # 告警冷却时间（秒），避免重复告警
  cooldown_period: 300

# 输出配置
output:
  # 控制台输出配置
  console:
    enabled: true
    refresh_interval: 5           # 控制台刷新间隔（秒）
    show_empty_groups: false      # 是否显示空的消费者组
    show_stable_groups: true      # 是否显示稳定状态的消费者组
    max_display_groups: 50        # 最大显示的消费者组数量
  
  # 文件输出配置
  file:
    enabled: true
    output_dir: "./reports"       # 输出目录
    
    # 事件日志配置
    event_log:
      enabled: true
      filename: "events.log"
      max_size_mb: 100            # 日志文件最大大小（MB）
      backup_count: 5             # 备份文件数量
    
    # 监控报告配置
    reports:
      enabled: true
      format: "html"              # 报告格式：html, markdown
      interval: 3600              # 报告生成间隔（秒）
      filename_template: "monitor_report_{timestamp}.{format}"

# 日志配置
logging:
  level: "INFO"                   # 日志级别：DEBUG, INFO, WARNING, ERROR, CRITICAL
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  
  # 文件日志配置
  file:
    enabled: true
    filename: "kafka_monitor.log"
    max_size_mb: 50
    backup_count: 3
  
  # 控制台日志配置
  console:
    enabled: true
    level: "INFO"
