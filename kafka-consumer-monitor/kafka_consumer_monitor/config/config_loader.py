"""
配置文件加载器

提供配置文件加载、验证和管理功能，支持YAML格式配置文件
"""

import os
import yaml
from typing import Dict, Any, Optional, List
from pathlib import Path


class ConfigLoader:
    """
    配置文件加载器
    
    负责加载、验证和管理配置文件，支持默认配置和用户自定义配置的合并
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """
        初始化配置加载器
        
        参数:
            config_path: 配置文件路径，如果为None则使用默认配置
        """
        self.config_path = config_path
        self.config = {}
        self._load_config()
    
    def _load_config(self):
        """
        加载配置文件
        
        首先加载默认配置，然后如果指定了用户配置文件，则合并用户配置
        """
        # 加载默认配置
        default_config_path = Path(__file__).parent / "default_config.yaml"
        self.config = self._load_yaml_file(default_config_path)
        
        # 如果指定了用户配置文件，则合并配置
        if self.config_path and os.path.exists(self.config_path):
            user_config = self._load_yaml_file(self.config_path)
            self.config = self._merge_configs(self.config, user_config)
        
        # 验证配置
        self._validate_config()
    
    def _load_yaml_file(self, file_path: Path) -> Dict[str, Any]:
        """
        加载YAML配置文件
        
        参数:
            file_path: 配置文件路径
            
        返回值:
            Dict[str, Any]: 配置字典
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f) or {}
        except Exception as e:
            raise ValueError(f"加载配置文件失败: {file_path}, 错误: {e}")
    
    def _merge_configs(self, default_config: Dict[str, Any], user_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        合并默认配置和用户配置
        
        参数:
            default_config: 默认配置
            user_config: 用户配置
            
        返回值:
            Dict[str, Any]: 合并后的配置
        """
        merged = default_config.copy()
        
        for key, value in user_config.items():
            if key in merged and isinstance(merged[key], dict) and isinstance(value, dict):
                merged[key] = self._merge_configs(merged[key], value)
            else:
                merged[key] = value
        
        return merged
    
    def _validate_config(self):
        """
        验证配置文件的有效性
        
        检查必需的配置项是否存在，配置值是否合理
        """
        # 验证Kafka配置
        kafka_config = self.config.get('kafka', {})
        if not kafka_config.get('bootstrap_servers'):
            raise ValueError("Kafka bootstrap_servers配置不能为空")
        
        # 验证监控间隔
        monitoring_config = self.config.get('monitoring', {})
        interval = monitoring_config.get('interval', 30)
        if not isinstance(interval, (int, float)) or interval <= 0:
            raise ValueError("监控间隔必须是正数")
        
        # 验证告警阈值
        alerts_config = self.config.get('alerts', {})
        lag_threshold = alerts_config.get('lag_threshold', 1000)
        if not isinstance(lag_threshold, int) or lag_threshold < 0:
            raise ValueError("积压告警阈值必须是非负整数")
        
        # 验证输出目录
        output_config = self.config.get('output', {})
        file_config = output_config.get('file', {})
        if file_config.get('enabled', True):
            output_dir = file_config.get('output_dir', './reports')
            try:
                Path(output_dir).mkdir(parents=True, exist_ok=True)
            except Exception as e:
                raise ValueError(f"无法创建输出目录 {output_dir}: {e}")
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值，支持点号分隔的嵌套键
        
        参数:
            key: 配置键，支持点号分隔的嵌套键，如 'kafka.bootstrap_servers'
            default: 默认值
            
        返回值:
            Any: 配置值
        """
        keys = key.split('.')
        value = self.config
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def get_kafka_config(self) -> Dict[str, Any]:
        """
        获取Kafka连接配置
        
        返回值:
            Dict[str, Any]: Kafka配置字典
        """
        kafka_config = self.config.get('kafka', {}).copy()
        
        # 过滤掉None值的配置项
        return {k: v for k, v in kafka_config.items() if v is not None}
    
    def get_monitoring_config(self) -> Dict[str, Any]:
        """
        获取监控配置
        
        返回值:
            Dict[str, Any]: 监控配置字典
        """
        return self.config.get('monitoring', {})
    
    def get_alerts_config(self) -> Dict[str, Any]:
        """
        获取告警配置
        
        返回值:
            Dict[str, Any]: 告警配置字典
        """
        return self.config.get('alerts', {})
    
    def get_output_config(self) -> Dict[str, Any]:
        """
        获取输出配置
        
        返回值:
            Dict[str, Any]: 输出配置字典
        """
        return self.config.get('output', {})
    
    def get_logging_config(self) -> Dict[str, Any]:
        """
        获取日志配置
        
        返回值:
            Dict[str, Any]: 日志配置字典
        """
        return self.config.get('logging', {})
    
    def should_monitor_group(self, group_id: str) -> bool:
        """
        检查是否应该监控指定的消费者组
        
        参数:
            group_id: 消费者组ID
            
        返回值:
            bool: 是否应该监控
        """
        monitoring_config = self.get_monitoring_config()
        
        # 检查排除列表
        exclude_groups = monitoring_config.get('exclude_groups', [])
        if group_id in exclude_groups:
            return False
        
        # 检查包含列表
        consumer_groups = monitoring_config.get('consumer_groups', [])
        if consumer_groups:
            return group_id in consumer_groups
        
        return True
    
    def should_monitor_topic(self, topic: str) -> bool:
        """
        检查是否应该监控指定的主题
        
        参数:
            topic: 主题名称
            
        返回值:
            bool: 是否应该监控
        """
        monitoring_config = self.get_monitoring_config()
        
        # 检查排除列表
        exclude_topics = monitoring_config.get('exclude_topics', [])
        if topic in exclude_topics:
            return False
        
        # 检查包含列表
        topics = monitoring_config.get('topics', [])
        if topics:
            return topic in topics
        
        return True
