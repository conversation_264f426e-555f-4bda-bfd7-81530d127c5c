"""
事件跟踪器

负责跟踪和记录消费者组状态变化、重平衡事件等
"""

import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta

from ..models.consumer_group import ConsumerGroup, ConsumerGroupState
from ..models.event import Event, EventType, EventSeverity, RebalanceEvent, LagEvent


logger = logging.getLogger(__name__)


class EventTracker:
    """
    事件跟踪器
    
    负责监控消费者组状态变化，检测和记录各种事件
    """
    
    def __init__(self, lag_threshold: int = 1000):
        """
        初始化事件跟踪器
        
        参数:
            lag_threshold: 积压告警阈值
        """
        self.lag_threshold = lag_threshold
        self.previous_states = {}  # 存储上一次的消费者组状态
        self.rebalance_start_times = {}  # 记录重平衡开始时间
        self.events = []  # 存储事件列表
    
    def track_consumer_group(self, consumer_group: ConsumerGroup) -> List[Event]:
        """
        跟踪消费者组状态变化并生成事件
        
        参数:
            consumer_group: 当前消费者组状态
            
        返回值:
            List[Event]: 检测到的事件列表
        """
        events = []
        group_id = consumer_group.group_id
        
        # 检查状态变化事件
        state_events = self._check_state_changes(consumer_group)
        events.extend(state_events)
        
        # 检查重平衡事件
        rebalance_events = self._check_rebalance_events(consumer_group)
        events.extend(rebalance_events)
        
        # 检查成员变化事件
        member_events = self._check_member_changes(consumer_group)
        events.extend(member_events)
        
        # 检查积压告警事件
        lag_events = self._check_lag_threshold(consumer_group)
        events.extend(lag_events)
        
        # 检查分区分配变化事件
        assignment_events = self._check_partition_assignment_changes(consumer_group)
        events.extend(assignment_events)
        
        # 更新状态记录
        self._update_previous_state(consumer_group)
        
        # 存储事件
        self.events.extend(events)
        
        # 清理过期事件（保留24小时内的事件）
        self._cleanup_old_events()
        
        return events
    
    def _check_state_changes(self, consumer_group: ConsumerGroup) -> List[Event]:
        """
        检查消费者组状态变化
        
        参数:
            consumer_group: 消费者组对象
            
        返回值:
            List[Event]: 状态变化事件列表
        """
        events = []
        group_id = consumer_group.group_id
        current_state = consumer_group.state
        
        if group_id in self.previous_states:
            previous_state = self.previous_states[group_id]['state']
            
            if previous_state != current_state:
                # 确定事件严重程度
                severity = EventSeverity.INFO
                if current_state == ConsumerGroupState.DEAD:
                    severity = EventSeverity.ERROR
                elif current_state in [ConsumerGroupState.PREPARING_REBALANCE, 
                                     ConsumerGroupState.COMPLETING_REBALANCE]:
                    severity = EventSeverity.WARNING
                
                event = Event(
                    event_type=EventType.GROUP_STATE_CHANGE,
                    group_id=group_id,
                    severity=severity,
                    message=f"消费者组状态从 {previous_state.value} 变更为 {current_state.value}",
                    details={
                        'previous_state': previous_state.value,
                        'current_state': current_state.value,
                        'member_count': consumer_group.member_count,
                        'total_lag': consumer_group.total_lag
                    }
                )
                events.append(event)
        else:
            # 新发现的消费者组
            event = Event(
                event_type=EventType.CONSUMER_GROUP_CREATED,
                group_id=group_id,
                severity=EventSeverity.INFO,
                message=f"发现新的消费者组: {group_id}",
                details={
                    'state': current_state.value,
                    'member_count': consumer_group.member_count,
                    'topics': consumer_group.topics
                }
            )
            events.append(event)
        
        return events
    
    def _check_rebalance_events(self, consumer_group: ConsumerGroup) -> List[Event]:
        """
        检查重平衡事件
        
        参数:
            consumer_group: 消费者组对象
            
        返回值:
            List[Event]: 重平衡事件列表
        """
        events = []
        group_id = consumer_group.group_id
        current_state = consumer_group.state
        
        # 检查重平衡开始
        if current_state == ConsumerGroupState.PREPARING_REBALANCE:
            if group_id not in self.rebalance_start_times:
                self.rebalance_start_times[group_id] = datetime.now()
                
                # 分析重平衡触发原因
                trigger_reason = self._analyze_rebalance_trigger(consumer_group)
                
                event = RebalanceEvent(
                    event_type=EventType.REBALANCE_START,
                    group_id=group_id,
                    severity=EventSeverity.WARNING,
                    message=f"消费者组开始重平衡",
                    trigger_reason=trigger_reason,
                    participating_members=[m.member_id for m in consumer_group.members]
                )
                events.append(event)
        
        # 检查重平衡完成
        elif current_state == ConsumerGroupState.STABLE and group_id in self.rebalance_start_times:
            start_time = self.rebalance_start_times[group_id]
            duration_ms = int((datetime.now() - start_time).total_seconds() * 1000)
            
            # 分析分区重新分配
            partition_reassignments = self._analyze_partition_reassignments(consumer_group)
            
            event = RebalanceEvent(
                event_type=EventType.REBALANCE_COMPLETE,
                group_id=group_id,
                severity=EventSeverity.INFO,
                message=f"消费者组重平衡完成，耗时 {duration_ms}ms",
                duration_ms=duration_ms,
                participating_members=[m.member_id for m in consumer_group.members],
                partition_reassignments=partition_reassignments
            )
            events.append(event)
            
            # 清理重平衡开始时间记录
            del self.rebalance_start_times[group_id]
        
        return events
    
    def _check_member_changes(self, consumer_group: ConsumerGroup) -> List[Event]:
        """
        检查消费者组成员变化
        
        参数:
            consumer_group: 消费者组对象
            
        返回值:
            List[Event]: 成员变化事件列表
        """
        events = []
        group_id = consumer_group.group_id
        current_members = set(m.member_id for m in consumer_group.members)
        
        if group_id in self.previous_states:
            previous_members = set(self.previous_states[group_id]['members'])
            
            # 检查新加入的成员
            joined_members = current_members - previous_members
            for member_id in joined_members:
                event = Event(
                    event_type=EventType.MEMBER_JOIN,
                    group_id=group_id,
                    severity=EventSeverity.INFO,
                    message=f"消费者 {member_id} 加入消费者组",
                    details={'member_id': member_id}
                )
                events.append(event)
            
            # 检查离开的成员
            left_members = previous_members - current_members
            for member_id in left_members:
                event = Event(
                    event_type=EventType.MEMBER_LEAVE,
                    group_id=group_id,
                    severity=EventSeverity.WARNING,
                    message=f"消费者 {member_id} 离开消费者组",
                    details={'member_id': member_id}
                )
                events.append(event)
        
        return events
    
    def _check_lag_threshold(self, consumer_group: ConsumerGroup) -> List[Event]:
        """
        检查积压阈值告警
        
        参数:
            consumer_group: 消费者组对象
            
        返回值:
            List[Event]: 积压告警事件列表
        """
        events = []
        total_lag = consumer_group.total_lag
        
        if total_lag > self.lag_threshold:
            # 分析受影响的主题和分区
            affected_topics = []
            partition_lags = {}
            
            for partition in consumer_group.lagging_partitions:
                if partition.topic not in affected_topics:
                    affected_topics.append(partition.topic)
                partition_key = f"{partition.topic}-{partition.partition}"
                partition_lags[partition_key] = partition.lag
            
            event = LagEvent(
                event_type=EventType.LAG_THRESHOLD_EXCEEDED,
                group_id=consumer_group.group_id,
                severity=EventSeverity.WARNING if total_lag < self.lag_threshold * 2 else EventSeverity.ERROR,
                message=f"消费者组积压超过阈值: {total_lag} > {self.lag_threshold}",
                total_lag=total_lag,
                threshold=self.lag_threshold,
                affected_topics=affected_topics,
                partition_lags=partition_lags
            )
            events.append(event)
        
        return events
    
    def _check_partition_assignment_changes(self, consumer_group: ConsumerGroup) -> List[Event]:
        """
        检查分区分配变化
        
        参数:
            consumer_group: 消费者组对象
            
        返回值:
            List[Event]: 分区分配变化事件列表
        """
        events = []
        group_id = consumer_group.group_id
        
        if group_id in self.previous_states:
            current_assignments = self._get_partition_assignments(consumer_group)
            previous_assignments = self.previous_states[group_id].get('assignments', {})
            
            if current_assignments != previous_assignments:
                event = Event(
                    event_type=EventType.PARTITION_ASSIGNMENT_CHANGE,
                    group_id=group_id,
                    severity=EventSeverity.INFO,
                    message="分区分配发生变化",
                    details={
                        'previous_assignments': previous_assignments,
                        'current_assignments': current_assignments
                    }
                )
                events.append(event)
        
        return events
    
    def _analyze_rebalance_trigger(self, consumer_group: ConsumerGroup) -> str:
        """
        分析重平衡触发原因
        
        参数:
            consumer_group: 消费者组对象
            
        返回值:
            str: 触发原因描述
        """
        group_id = consumer_group.group_id
        
        if group_id not in self.previous_states:
            return "消费者组首次启动"
        
        previous_member_count = len(self.previous_states[group_id]['members'])
        current_member_count = consumer_group.member_count
        
        if current_member_count > previous_member_count:
            return "消费者加入"
        elif current_member_count < previous_member_count:
            return "消费者离开"
        else:
            return "会话超时或其他原因"
    
    def _analyze_partition_reassignments(self, consumer_group: ConsumerGroup) -> Dict[str, List[int]]:
        """
        分析分区重新分配情况
        
        参数:
            consumer_group: 消费者组对象
            
        返回值:
            Dict[str, List[int]]: 主题到分区列表的映射
        """
        assignments = {}
        for partition in consumer_group.partitions:
            if partition.topic not in assignments:
                assignments[partition.topic] = []
            assignments[partition.topic].append(partition.partition)
        
        return assignments
    
    def _get_partition_assignments(self, consumer_group: ConsumerGroup) -> Dict[str, str]:
        """
        获取分区分配信息
        
        参数:
            consumer_group: 消费者组对象
            
        返回值:
            Dict[str, str]: 分区到消费者的映射
        """
        assignments = {}
        for partition in consumer_group.partitions:
            if partition.consumer_id:
                partition_key = f"{partition.topic}-{partition.partition}"
                assignments[partition_key] = partition.consumer_id
        
        return assignments
    
    def _update_previous_state(self, consumer_group: ConsumerGroup):
        """
        更新上一次状态记录
        
        参数:
            consumer_group: 消费者组对象
        """
        self.previous_states[consumer_group.group_id] = {
            'state': consumer_group.state,
            'members': set(m.member_id for m in consumer_group.members),
            'assignments': self._get_partition_assignments(consumer_group),
            'timestamp': datetime.now()
        }
    
    def _cleanup_old_events(self):
        """
        清理过期事件
        """
        cutoff_time = datetime.now() - timedelta(hours=24)
        self.events = [event for event in self.events if event.timestamp > cutoff_time]
    
    def get_recent_events(self, group_id: Optional[str] = None, hours: int = 1) -> List[Event]:
        """
        获取最近的事件
        
        参数:
            group_id: 消费者组ID，为None时返回所有组的事件
            hours: 时间范围（小时）
            
        返回值:
            List[Event]: 事件列表
        """
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        filtered_events = [
            event for event in self.events
            if event.timestamp > cutoff_time and (group_id is None or event.group_id == group_id)
        ]
        
        return sorted(filtered_events, key=lambda e: e.timestamp, reverse=True)
