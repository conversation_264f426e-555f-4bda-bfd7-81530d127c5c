2025-08-15 16:00:37,075 - __main__ - INFO - 正在启动Kafka消费者组监控程序...
2025-08-15 16:00:37,080 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:9092 <connecting> [IPv4 ('**************', 9092)]>: connecting to **************:9092 [('**************', 9092) IPv4]
2025-08-15 16:00:37,080 - kafka.conn - INFO - Probing node bootstrap-0 broker version
2025-08-15 16:00:37,110 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:9092 <connecting> [IPv4 ('**************', 9092)]>: Connection complete.
2025-08-15 16:00:37,247 - kafka.conn - INFO - Broker version identified as 2.4.0
2025-08-15 16:00:37,247 - kafka.conn - INFO - Set configuration api_version=(2, 4, 0) to skip auto check_version requests on startup
2025-08-15 16:00:37,247 - kafka.conn - INFO - Probing node bootstrap-0 broker version
2025-08-15 16:00:37,382 - kafka.conn - INFO - Broker version identified as 2.4.0
2025-08-15 16:00:37,382 - kafka.conn - INFO - Set configuration api_version=(2, 4, 0) to skip auto check_version requests on startup
2025-08-15 16:00:37,497 - kafka.conn - INFO - <BrokerConnection node_id=1001 host=**************:9092 <connecting> [IPv4 ('**************', 9092)]>: connecting to **************:9092 [('**************', 9092) IPv4]
2025-08-15 16:00:37,498 - kafka.conn - INFO - Probing node 1001 broker version
2025-08-15 16:00:37,559 - kafka.conn - INFO - <BrokerConnection node_id=1001 host=**************:9092 <connecting> [IPv4 ('**************', 9092)]>: Connection complete.
2025-08-15 16:00:37,560 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:9092 <connected> [IPv4 ('**************', 9092)]>: Closing connection. 
2025-08-15 16:00:37,690 - kafka.conn - INFO - Broker version identified as 2.4.0
2025-08-15 16:00:37,690 - kafka.conn - INFO - Set configuration api_version=(2, 4, 0) to skip auto check_version requests on startup
2025-08-15 16:00:37,691 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:9092 <connecting> [IPv4 ('**************', 9092)]>: connecting to **************:9092 [('**************', 9092) IPv4]
2025-08-15 16:00:37,691 - kafka.conn - INFO - Probing node bootstrap-0 broker version
2025-08-15 16:00:37,725 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:9092 <connecting> [IPv4 ('**************', 9092)]>: Connection complete.
2025-08-15 16:00:37,887 - kafka.conn - INFO - Broker version identified as 2.4.0
2025-08-15 16:00:37,887 - kafka.conn - INFO - Set configuration api_version=(2, 4, 0) to skip auto check_version requests on startup
2025-08-15 16:00:37,888 - kafka_consumer_monitor.core.kafka_client - INFO - Kafka客户端连接成功
2025-08-15 16:00:37,889 - kafka_consumer_monitor.core.consumer_group_monitor - INFO - 消费者组监控器启动成功
2025-08-15 16:00:37,889 - kafka_consumer_monitor.reporters.file_reporter - INFO - 文件报告生成器已启动
2025-08-15 16:00:37,889 - __main__ - INFO - Kafka消费者组监控程序启动成功
2025-08-15 16:00:38,359 - kafka.conn - INFO - <BrokerConnection node_id=1001 host=**************:9092 <connecting> [IPv4 ('**************', 9092)]>: connecting to **************:9092 [('**************', 9092) IPv4]
2025-08-15 16:00:38,465 - kafka.conn - INFO - <BrokerConnection node_id=1001 host=**************:9092 <connecting> [IPv4 ('**************', 9092)]>: Connection complete.
2025-08-15 16:00:38,465 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:9092 <connected> [IPv4 ('**************', 9092)]>: Closing connection. 
2025-08-15 16:00:40,510 - kafka_consumer_monitor.reporters.alert_manager - INFO - 告警已处理: moye-data-send-monitor-record-group - 消费者组积压超过阈值: 18313 > 1000
2025-08-15 16:00:40,511 - kafka_consumer_monitor.reporters.alert_manager - INFO - 告警已处理: moye_record_msg_cloud_group - 消费者组积压超过阈值: 10800 > 1000
2025-08-15 16:00:40,511 - kafka_consumer_monitor.reporters.alert_manager - INFO - 告警已处理: moye_tracer_msg_cloud_new_group - 消费者组积压超过阈值: 9546 > 1000
2025-08-15 16:01:29,349 - __main__ - INFO - 收到信号 2，正在优雅关闭...
2025-08-15 16:01:29,350 - __main__ - INFO - 正在停止Kafka消费者组监控程序...
2025-08-15 16:01:29,351 - kafka_consumer_monitor.core.consumer_group_monitor - INFO - 正在停止消费者组监控器...
2025-08-15 16:01:37,898 - kafka.conn - INFO - <BrokerConnection node_id=1001 host=**************:9092 <connected> [IPv4 ('**************', 9092)]>: Closing connection. 
2025-08-15 16:01:37,900 - kafka.conn - INFO - <BrokerConnection node_id=1001 host=**************:9092 <connected> [IPv4 ('**************', 9092)]>: Closing connection. 
2025-08-15 16:01:37,900 - kafka_consumer_monitor.core.kafka_client - INFO - Kafka客户端连接已关闭
2025-08-15 16:01:37,900 - kafka_consumer_monitor.core.consumer_group_monitor - INFO - 消费者组监控器已停止
2025-08-15 16:01:43,126 - kafka_consumer_monitor.reporters.file_reporter - INFO - 文件报告生成器已停止
2025-08-15 16:01:43,127 - __main__ - INFO - Kafka消费者组监控程序已停止
