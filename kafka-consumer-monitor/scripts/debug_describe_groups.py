#!/usr/bin/env python3
"""
调试describe_consumer_groups API返回值格式

这个脚本用于分析kafka-python-ng的describe_consumer_groups方法的实际返回值
"""

def debug_describe_groups():
    """调试describe_consumer_groups API"""
    print("开始调试describe_consumer_groups API...")
    
    try:
        from kafka import KafkaAdminClient
        from kafka.errors import NoBrokersAvailable
        
        # 使用默认配置连接
        config = {
            'bootstrap_servers': ['localhost:9092'],
            'request_timeout_ms': 10000,
        }
        
        admin_client = KafkaAdminClient(**config)
        print("✅ AdminClient创建成功")
        
        # 首先获取消费者组列表
        print("\n1. 获取消费者组列表...")
        try:
            groups = admin_client.list_consumer_groups()
            print(f"✅ 找到 {len(groups)} 个消费者组:")
            for i, group in enumerate(groups):
                print(f"   {i+1}. {group}")
            
            if not groups:
                print("❌ 没有找到消费者组，无法继续测试")
                return
            
            # 取第一个消费者组进行测试
            test_group = groups[0][0]  # 取tuple的第一个元素（group_name）
            print(f"\n2. 测试消费者组: {test_group}")
            
        except Exception as e:
            print(f"❌ 获取消费者组列表失败: {e}")
            return
        
        # 测试describe_consumer_groups
        print(f"\n3. 调用describe_consumer_groups(['{test_group}'])...")
        try:
            result = admin_client.describe_consumer_groups([test_group])
            
            print(f"✅ describe_consumer_groups调用成功")
            print(f"   返回值类型: {type(result)}")
            print(f"   返回值长度: {len(result) if hasattr(result, '__len__') else 'N/A'}")
            
            if isinstance(result, list):
                print(f"   这是一个列表，包含 {len(result)} 个元素")
                if result:
                    print(f"   第一个元素类型: {type(result[0])}")
                    print(f"   第一个元素内容: {result[0]}")
                    
                    # 检查第一个元素的属性
                    if hasattr(result[0], '__dict__'):
                        print(f"   第一个元素的属性: {list(result[0].__dict__.keys())}")
                    elif hasattr(result[0], '_fields'):
                        print(f"   第一个元素的字段: {result[0]._fields}")
                        
            elif isinstance(result, dict):
                print(f"   这是一个字典，键: {list(result.keys())}")
                if result:
                    first_key = list(result.keys())[0]
                    print(f"   第一个值的类型: {type(result[first_key])}")
                    print(f"   第一个值的内容: {result[first_key]}")
            else:
                print(f"   未知类型，内容: {result}")
                
        except Exception as e:
            print(f"❌ describe_consumer_groups调用失败: {e}")
            import traceback
            traceback.print_exc()
        
        # 测试多个消费者组
        if len(groups) > 1:
            print(f"\n4. 测试多个消费者组...")
            test_groups = [group[0] for group in groups[:3]]  # 最多测试3个
            print(f"   测试组: {test_groups}")
            
            try:
                result = admin_client.describe_consumer_groups(test_groups)
                print(f"✅ 多组describe_consumer_groups调用成功")
                print(f"   返回值类型: {type(result)}")
                print(f"   返回值长度: {len(result) if hasattr(result, '__len__') else 'N/A'}")
                
                if isinstance(result, list):
                    print(f"   列表中每个元素的类型:")
                    for i, item in enumerate(result):
                        print(f"     [{i}] {type(item)}: {item}")
                        
            except Exception as e:
                print(f"❌ 多组describe_consumer_groups调用失败: {e}")
        
    except NoBrokersAvailable:
        print("❌ 无法连接到Kafka集群")
    except Exception as e:
        print(f"❌ 调试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


def debug_current_implementation():
    """调试当前实现的问题"""
    print("\n" + "="*60)
    print("调试当前实现的问题...")
    
    try:
        from kafka_consumer_monitor.core.kafka_client import KafkaClient
        from kafka_consumer_monitor.config.config_loader import ConfigLoader
        
        # 加载配置
        config_loader = ConfigLoader()
        kafka_config = config_loader.get_kafka_config()
        
        # 创建客户端
        kafka_client = KafkaClient(kafka_config)
        print("✅ KafkaClient创建成功")
        
        # 获取消费者组列表
        groups = kafka_client.get_consumer_groups()
        print(f"✅ 获取到 {len(groups)} 个消费者组: {groups}")
        
        if groups:
            test_group = groups[0]
            print(f"\n测试获取消费者组详情: {test_group}")
            
            # 直接调用admin_client的describe_consumer_groups
            print("1. 直接调用admin_client.describe_consumer_groups...")
            try:
                raw_result = kafka_client.admin_client.describe_consumer_groups([test_group])
                print(f"   原始返回值类型: {type(raw_result)}")
                print(f"   原始返回值: {raw_result}")
                
                # 检查是否是列表
                if isinstance(raw_result, list):
                    print(f"   这是列表，长度: {len(raw_result)}")
                    if raw_result:
                        print(f"   第一个元素: {raw_result[0]}")
                        if hasattr(raw_result[0], 'group_id'):
                            print(f"   第一个元素的group_id: {raw_result[0].group_id}")
                        else:
                            print(f"   第一个元素没有group_id属性")
                            print(f"   第一个元素的属性: {dir(raw_result[0])}")
                
            except Exception as e:
                print(f"   ❌ 直接调用失败: {e}")
                import traceback
                traceback.print_exc()
            
            # 调用我们的封装方法
            print("\n2. 调用封装的get_consumer_group_info方法...")
            try:
                group_info = kafka_client.get_consumer_group_info(test_group)
                if group_info:
                    print(f"   ✅ 成功获取消费者组信息: {group_info.group_id}")
                else:
                    print(f"   ❌ 获取消费者组信息失败")
                    
            except Exception as e:
                print(f"   ❌ 封装方法调用失败: {e}")
                import traceback
                traceback.print_exc()
        
        kafka_client.close()
        
    except Exception as e:
        print(f"❌ 调试当前实现失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    print("Kafka describe_consumer_groups API 调试")
    print("=" * 60)
    
    debug_describe_groups()
    debug_current_implementation()
    
    print("\n" + "=" * 60)
    print("调试完成！")
