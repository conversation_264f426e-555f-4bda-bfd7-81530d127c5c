# Kafka消费者组监控程序

一个功能完整的Kafka消费者组监控工具，提供实时监控、事件跟踪、告警通知和报告生成等功能。

## 功能特性

### 核心功能
- **实时监控**: 监控Kafka集群中的消费者组状态和积压情况
- **积压分析**: 实时显示每个消费者组的lag信息，包括分区级别的积压数量
- **事件跟踪**: 记录消费者组状态变化事件，特别关注重平衡（rebalance）事件
- **趋势分析**: 提供积压趋势分析和异常检测功能

### 监控内容
- **消费者组基本信息**: 组ID、状态（Stable/PreparingRebalance/CompletingRebalance/Dead等）
- **积压监控**: 每个分区的当前offset、最新offset、lag数量，消费者组积压总数
- **消费者实例信息**: 消费者ID、分配的分区、消费者客户端信息
- **重平衡事件分析**: 触发原因、参与成员、分区重分配情况、持续时间

### 输出功能
- **控制台实时显示**: 彩色控制台界面，实时刷新监控数据
- **文件报告生成**: 支持HTML和Markdown格式的监控报告
- **事件日志记录**: 详细记录所有状态变化和事件
- **告警通知**: 支持多种告警方式，可配置告警阈值

## 安装说明

### 环境要求
- Python 3.7+
- Kafka集群访问权限

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd kafka-consumer-monitor
```

2. **安装依赖**
```bash
pip install -r requirements.txt
```

3. **配置文件**
```bash
# 复制默认配置文件
cp kafka_consumer_monitor/config/default_config.yaml config.yaml
# 根据实际环境修改配置
vim config.yaml
```

## 配置说明

### 基本配置示例

```yaml
# Kafka集群连接配置
kafka:
  bootstrap_servers:
    - "localhost:9092"
  security_protocol: "PLAINTEXT"
  request_timeout_ms: 30000

# 监控配置
monitoring:
  interval: 30                    # 监控间隔（秒）
  consumer_groups: []             # 要监控的消费者组列表（空则监控所有）
  exclude_groups: []              # 要排除的消费者组列表
  topics: []                      # 要监控的主题列表（空则监控所有）
  exclude_topics: []              # 要排除的主题列表

# 告警配置
alerts:
  enabled: true
  lag_threshold: 1000             # 积压告警阈值
  rebalance_duration_threshold: 60 # 重平衡持续时间告警阈值（秒）
  cooldown_period: 300            # 告警冷却时间（秒）

# 输出配置
output:
  console:
    enabled: true
    refresh_interval: 5           # 控制台刷新间隔（秒）
    show_empty_groups: false      # 是否显示空的消费者组
    max_display_groups: 50        # 最大显示的消费者组数量
  
  file:
    enabled: true
    output_dir: "./reports"       # 输出目录
    
    event_log:
      enabled: true
      filename: "events.log"
      max_size_mb: 100
      backup_count: 5
    
    reports:
      enabled: true
      format: "html"              # 报告格式：html, markdown
      interval: 3600              # 报告生成间隔（秒）
      filename_template: "monitor_report_{timestamp}.{format}"

# 日志配置
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  
  file:
    enabled: true
    filename: "kafka_monitor.log"
    max_size_mb: 50
    backup_count: 3
  
  console:
    enabled: true
    level: "INFO"
```

### 安全配置示例

```yaml
kafka:
  bootstrap_servers:
    - "kafka1.example.com:9092"
    - "kafka2.example.com:9092"
  
  # SASL/SCRAM认证
  security_protocol: "SASL_SSL"
  sasl_mechanism: "SCRAM-SHA-256"
  sasl_username: "monitor_user"
  sasl_password: "your_password"
  
  # SSL配置
  ssl_cafile: "/path/to/ca-cert.pem"
  ssl_certfile: "/path/to/client-cert.pem"
  ssl_keyfile: "/path/to/client-key.pem"
  ssl_check_hostname: true
```

## 使用方法

### 启动监控服务

```bash
# 使用默认配置启动
python main.py start

# 使用自定义配置文件启动
python main.py -c config.yaml start

# 启用详细输出
python main.py -v -c config.yaml start
```

### 查看消费者组状态

```bash
# 查看所有消费者组状态
python main.py -c config.yaml status

# 查看指定消费者组状态
python main.py -c config.yaml status --group my-consumer-group

# 以JSON格式输出
python main.py -c config.yaml status --format json
```

### 查看最近事件

```bash
# 查看最近1小时的事件
python main.py -c config.yaml events

# 查看最近6小时的事件
python main.py -c config.yaml events --hours 6

# 查看指定消费者组的事件
python main.py -c config.yaml events --group my-consumer-group
```

## 项目结构

```
kafka_consumer_monitor/
├── config/                     # 配置管理
│   ├── config_loader.py       # 配置文件加载器
│   └── default_config.yaml    # 默认配置文件
├── core/                      # 核心监控逻辑
│   ├── kafka_client.py        # Kafka客户端封装
│   ├── consumer_group_monitor.py # 消费者组监控器
│   ├── lag_calculator.py      # Lag计算器
│   └── event_tracker.py       # 事件跟踪器
├── models/                    # 数据模型
│   ├── consumer_group.py      # 消费者组数据模型
│   ├── partition_info.py      # 分区信息模型
│   └── event.py              # 事件模型
├── reporters/                 # 输出和报告
│   ├── console_reporter.py    # 控制台输出
│   ├── file_reporter.py       # 文件报告生成器
│   └── alert_manager.py       # 告警管理器
├── utils/                     # 工具类
│   ├── logger.py             # 日志工具
│   └── formatter.py          # 格式化工具
└── __init__.py
main.py                        # 主程序入口
requirements.txt               # 依赖包列表
README.md                      # 使用说明
```

## 监控界面示例

### 控制台输出
```
============================================================
                    Kafka 消费者组监控器
============================================================
当前时间: 2024-01-15 14:30:25

监控状态
----------------------------------------
状态: 运行中
运行时间: 2.5h
监控周期: 150
监控组数: 5
错误次数: 0
最后更新: 14:30:20

消费者组概览
----------------------------------------
┌─────────────────┬────────┬────────┬──────────┬────────┬──────────────┐
│ 消费者组        │ 状态   │ 成员数 │ 总积压   │ 主题数 │ 最后更新     │
├─────────────────┼────────┼────────┼──────────┼────────┼──────────────┤
│ order-processor │ Stable │ 3      │ 1,250    │ 2      │ 14:30:15     │
│ user-analytics  │ Stable │ 2      │ 0        │ 1      │ 14:30:18     │
│ notification    │ Stable │ 1      │ 500      │ 3      │ 14:30:12     │
└─────────────────┴────────┴────────┴──────────┴────────┴──────────────┘

统计摘要:
总消费者组: 5, 稳定: 5, 重平衡中: 0
总积压: 1,750

最近事件 (最新10条)
----------------------------------------
14:25:30 [WARNING] order-processor: 消费者组积压超过阈值: 1250 > 1000
14:20:15 [INFO] user-analytics: 消费者 consumer-1 加入消费者组
14:15:45 [INFO] notification: 消费者组状态从 PreparingRebalance 变更为 Stable
```

### HTML报告示例

生成的HTML报告包含以下内容：
- 监控摘要统计
- 消费者组详细信息表格
- 告警事件列表
- 最近事件记录
- 可视化图表和趋势分析

## 告警功能

### 支持的告警类型
- **积压告警**: 当消费者组总积压超过配置阈值时触发
- **重平衡告警**: 当重平衡持续时间过长时触发
- **状态变化告警**: 当消费者组状态发生异常变化时触发
- **成员变化告警**: 当消费者成员加入或离开时触发

### 告警处理器
- **控制台告警**: 在控制台显示告警信息
- **文件告警**: 将告警写入日志文件
- **自定义告警**: 支持扩展自定义告警处理器

### 告警配置示例
```yaml
alerts:
  enabled: true
  lag_threshold: 1000                    # 积压超过1000时告警
  rebalance_duration_threshold: 60       # 重平衡超过60秒时告警
  cooldown_period: 300                   # 5分钟内不重复告警
```

## 事件类型说明

### 状态变化事件
- `GROUP_STATE_CHANGE`: 消费者组状态变化
- `CONSUMER_GROUP_CREATED`: 新消费者组创建
- `CONSUMER_GROUP_DELETED`: 消费者组删除

### 重平衡事件
- `REBALANCE_START`: 重平衡开始
- `REBALANCE_COMPLETE`: 重平衡完成

### 成员变化事件
- `MEMBER_JOIN`: 消费者加入
- `MEMBER_LEAVE`: 消费者离开

### 积压事件
- `LAG_THRESHOLD_EXCEEDED`: 积压超过阈值
- `PARTITION_ASSIGNMENT_CHANGE`: 分区分配变化

## 性能优化建议

### 监控间隔设置
- **高频监控**: 间隔10-30秒，适用于关键业务系统
- **常规监控**: 间隔30-60秒，适用于一般业务系统
- **低频监控**: 间隔60-300秒，适用于非关键系统

### 资源使用优化
- 合理设置`max_display_groups`限制显示的消费者组数量
- 定期清理历史事件和日志文件
- 根据集群规模调整`request_timeout_ms`

### 网络优化
- 将监控程序部署在靠近Kafka集群的网络环境中
- 使用连接池和连接复用减少网络开销
- 配置合适的超时时间避免长时间等待

## 故障排除

### 常见问题

**1. 无法连接到Kafka集群**
```
错误: NoBrokersAvailable
解决: 检查bootstrap_servers配置，确认网络连通性和端口开放
```

**2. 认证失败**
```
错误: Authentication failed
解决: 检查SASL用户名密码，确认用户权限
```

**3. SSL连接失败**
```
错误: SSL connection failed
解决: 检查SSL证书路径和有效性，确认hostname验证设置
```

**4. 监控数据不准确**
```
问题: 显示的lag数据与实际不符
解决: 检查时钟同步，调整监控间隔，确认offset提交策略
```

### 调试模式

启用详细日志输出：
```bash
# 修改配置文件中的日志级别
logging:
  level: "DEBUG"

# 或使用命令行参数
python main.py -v -c config.yaml start
```

### 日志分析

监控程序会生成以下日志文件：
- `kafka_monitor.log`: 主程序日志
- `events.log`: 事件记录日志
- `alerts.log`: 告警日志

## 扩展开发

### 自定义告警处理器

```python
from kafka_consumer_monitor.models.event import Event

class EmailAlertHandler:
    def __init__(self, smtp_config):
        self.smtp_config = smtp_config

    def __call__(self, event: Event):
        # 实现邮件发送逻辑
        self.send_email(event)

    def send_email(self, event):
        # 邮件发送实现
        pass

# 在主程序中注册
alert_manager.add_alert_handler(EmailAlertHandler(smtp_config))
```

### 自定义报告生成器

```python
from kafka_consumer_monitor.reporters.file_reporter import FileReporter

class CustomReporter(FileReporter):
    def _generate_custom_report(self, report_data):
        # 实现自定义报告格式
        pass
```

### 添加新的监控指标

```python
from kafka_consumer_monitor.core.lag_calculator import LagCalculator

class ExtendedLagCalculator(LagCalculator):
    def calculate_custom_metrics(self, consumer_group):
        # 实现自定义指标计算
        pass
```

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 贡献指南

欢迎提交Issue和Pull Request来改进这个项目。

### 开发环境设置
```bash
# 克隆项目
git clone <repository-url>
cd kafka-consumer-monitor

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows

# 安装开发依赖
pip install -r requirements.txt
pip install -r requirements-dev.txt  # 如果有开发依赖

# 运行测试
python -m pytest tests/
```

### 代码规范
- 使用Python 3.7+语法
- 遵循PEP 8代码风格
- 添加详细的中文注释
- 编写单元测试

## 更新日志

### v1.0.0 (2024-01-15)
- 初始版本发布
- 实现基本的消费者组监控功能
- 支持控制台和文件输出
- 提供告警和事件跟踪功能
- 支持HTML和Markdown报告生成

## 联系方式

如有问题或建议，请通过以下方式联系：
- 提交GitHub Issue
- 发送邮件至: [<EMAIL>]

---

**注意**: 本工具仅用于监控目的，不会修改Kafka集群的任何配置或数据。请确保监控用户具有适当的只读权限。
