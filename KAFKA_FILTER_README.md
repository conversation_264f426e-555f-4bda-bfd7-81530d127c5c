# Kafka数据过滤程序

一个功能完整的Kafka数据过滤程序，支持从指定的Kafka topic中消费JSON格式的消息数据，根据用户定义的过滤规则筛选符合条件的数据。

## 功能特性

### 核心功能
- 从指定的Kafka topic中消费JSON格式的消息数据
- 根据用户定义的过滤规则筛选符合条件的数据
- 支持复杂的多层嵌套字段访问（如：字段2.class2）
- 实时统计和性能监控

### 过滤规则支持

#### 逻辑运算符
- `AND`（且）：所有条件都必须满足
- `OR`（或）：任一条件满足即可
- `NOT`（非）：条件不满足

#### 比较运算符
- `=` 或 `==`：等于
- `>`：大于
- `<`：小于
- `>=`：大于等于
- `<=`：小于等于
- `IN`：包含在列表中
- `NOT IN`：不包含在列表中
- `CONTAINS`：字符串包含

#### 高级特性
- **括号分组**：支持使用括号对条件进行分组，控制运算优先级
- **多组条件**：支持定义多个独立的过滤规则组合
- **多层嵌套字段访问**：支持点号分隔的字段路径，如 `字段2.class2.子字段`

## 安装和配置

### 环境要求
- Python 3.7+
- kafka-python-ng 2.2.2+
- PyYAML 6.0.1+

### 安装依赖
```bash
pip install kafka-python-ng PyYAML colorama tabulate
```

### 配置文件
复制 `filter_config.yaml` 并根据实际环境修改配置：

```yaml
# Kafka连接配置
kafka:
  bootstrap_servers: "localhost:9092"
  topic: "your-topic"
  group_id: "kafka-data-filter-group"
  auto_offset_reset: "latest"

# 过滤规则配置
filter:
  rules:
    - "字段1 = 1 AND 字段2.class2 NOT IN (\"广告信息\", \"股票资讯\")"

# 输出配置
output:
  type: "console"
  format: "pretty_json"
```

## 使用方法

### 基本用法

#### 方法一：直接运行main.py（推荐）
```bash
# 使用默认配置运行
python kafka_data_filter/main.py filter_config.yaml

# 限制处理消息数量
python kafka_data_filter/main.py filter_config.yaml --max-messages 1000

# 启用详细输出
python kafka_data_filter/main.py filter_config.yaml --verbose

# 查看帮助信息
python kafka_data_filter/main.py --help

# 查看版本信息
python kafka_data_filter/main.py --version
```

#### 方法二：使用启动脚本
```bash
# 使用启动脚本运行
python kafka_filter_main.py filter_config.yaml

# 限制处理消息数量
python kafka_filter_main.py filter_config.yaml --max-messages 1000

# 启用详细输出
python kafka_filter_main.py filter_config.yaml --verbose
```

### 过滤规则示例

#### 基本字段过滤
```yaml
rules:
  - "status = \"active\" AND type IN (\"important\", \"urgent\")"
```

#### 数值比较
```yaml
rules:
  - "score > 80 AND rating >= 4.5"
```

#### 字符串包含
```yaml
rules:
  - "title CONTAINS \"重要\" AND content CONTAINS \"通知\""
```

#### 复杂逻辑组合
```yaml
rules:
  - "(priority = \"高\" OR urgency = \"紧急\") AND department IN (\"技术部\", \"产品部\")"
```

#### 多层嵌套字段访问
```yaml
rules:
  - "user.profile.level > 5 AND user.settings.notifications = true"
```

### 输出格式

#### 控制台输出（JSON格式）
```json
{
  "id": 123,
  "title": "重要通知",
  "content": "这是一条重要消息",
  "timestamp": "2024-01-01T10:00:00Z"
}
```

#### 文本格式输出
```
[2024-01-01 10:00:00] 过滤消息 #1
--------------------------------------------------
消息内容:
  id: 123
  title: 重要通知
  content: 这是一条重要消息

Kafka元数据:
  Topic: test-topic
  Partition: 0
  Offset: 12345
  Timestamp: 1704096000000
```

## 统计功能

程序提供实时统计信息，包括：

- **处理统计**：总消费数、处理数、通过数、过滤数
- **性能指标**：处理速度、吞吐量、平均处理时间
- **规则统计**：每个规则的通过率和执行情况

### 统计报告示例
```
==================================================
实时统计信息
==================================================
运行时间: 120.5 秒
消费消息数: 1500
处理消息数: 1500
通过过滤数: 450
被过滤数: 1050
输出消息数: 450
错误数: 0
通过率: 30.0%
平均处理时间: 2.5 ms
当前吞吐量: 12.5 消息/秒
总体吞吐量: 12.4 消息/秒
==================================================
```

## 高级配置

### 安全配置
支持SASL认证和SSL加密：

```yaml
kafka:
  security_protocol: "SASL_SSL"
  sasl_mechanism: "PLAIN"
  sasl_username: "your_username"
  sasl_password: "your_password"
```

### 性能调优
```yaml
kafka:
  # 批量拉取配置
  max_poll_records: 500
  fetch_min_bytes: 1024
  fetch_max_wait_ms: 500
  
statistics:
  # 统计窗口大小
  window_size_seconds: 60
  # 报告间隔
  interval: 5
```

## 错误处理

程序具有完善的错误处理机制：

- **连接错误**：自动重试Kafka连接
- **解析错误**：跳过无效的JSON消息
- **规则错误**：记录规则执行失败并继续处理
- **输出错误**：确保统计信息不丢失

## 日志记录

程序支持详细的日志记录：

```yaml
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR, CRITICAL
```

日志文件：`kafka_data_filter.log`

## 性能特性

- **高效解析**：使用递归下降解析器，性能优异
- **内存优化**：流式处理，内存占用稳定
- **并发安全**：统计模块线程安全
- **资源管理**：自动清理资源，支持优雅关闭

## 故障排除

### 常见问题

1. **连接失败**
   ```
   错误: Kafka连接失败
   解决: 检查bootstrap_servers配置和网络连接
   ```

2. **规则解析失败**
   ```
   错误: 表达式解析错误
   解决: 检查过滤规则语法，确保字段名和运算符正确
   ```

3. **消息解析失败**
   ```
   错误: JSON解析失败
   解决: 确保Kafka消息为有效的JSON格式
   ```

### 调试模式
启用DEBUG日志级别获取详细信息：

```yaml
logging:
  level: "DEBUG"
```

## 扩展开发

程序采用模块化设计，易于扩展：

- `config_manager.py`：配置管理
- `filter_parser.py`：规则解析器
- `data_accessor.py`：数据访问器
- `kafka_consumer.py`：Kafka消费者
- `data_filter.py`：数据过滤器
- `output_handler.py`：输出处理器
- `statistics.py`：统计模块

## 许可证

本程序遵循MIT许可证。
