# PythonProject 项目重构方案

## 项目识别

经过分析，PythonProject目录下存在两个独立的程序项目：

### 项目1：Kafka消费者组监控程序 (kafka-consumer-monitor)
- **功能**：监控Kafka集群中的消费者组状态、积压情况、事件跟踪
- **主要模块**：`kafka_consumer_monitor/`
- **主入口**：`main.py`

### 项目2：Kafka数据过滤程序 (kafka-data-filter)
- **功能**：从Kafka消费JSON消息并根据复杂规则进行过滤
- **主要模块**：`kafka_data_filter/`
- **主入口**：`kafka_data_filter/main.py` 和 `kafka_filter_main.py`

## 重构目标目录结构

```
PythonProject/
├── kafka-consumer-monitor/           # 项目1：Kafka消费者组监控程序
│   ├── README.md                    # 项目1专用文档
│   ├── requirements.txt             # 项目1依赖
│   ├── main.py                      # 项目1主入口
│   ├── config.example.yaml          # 项目1配置示例
│   ├── kafka_consumer_monitor/      # 项目1核心模块
│   │   ├── __init__.py
│   │   ├── config/
│   │   ├── core/
│   │   ├── models/
│   │   ├── reporters/
│   │   └── utils/
│   ├── reports/                     # 项目1报告目录
│   ├── logs/                        # 项目1日志目录
│   └── tests/                       # 项目1测试
│
├── kafka-data-filter/               # 项目2：Kafka数据过滤程序
│   ├── README.md                    # 项目2专用文档
│   ├── requirements.txt             # 项目2依赖
│   ├── main.py                      # 项目2主入口
│   ├── filter_config.yaml           # 项目2配置文件
│   ├── example_config.yaml          # 项目2配置示例
│   ├── kafka_data_filter/           # 项目2核心模块
│   │   ├── __init__.py
│   │   ├── config_manager.py
│   │   ├── data_accessor.py
│   │   ├── data_filter.py
│   │   ├── filter_parser.py
│   │   ├── kafka_consumer.py
│   │   ├── main.py
│   │   ├── output_handler.py
│   │   └── statistics.py
│   ├── output/                      # 项目2输出目录
│   ├── logs/                        # 项目2日志目录
│   ├── tests/                       # 项目2测试
│   └── scripts/                     # 项目2辅助脚本
│
└── docs/                            # 共享文档目录
    ├── PROJECT_OVERVIEW.md          # 项目总览
    └── DEVELOPMENT_GUIDE.md         # 开发指南
```

## 文件移动和重组计划

### 阶段1：创建新的目录结构

1. **创建项目1目录**：`kafka-consumer-monitor/`
2. **创建项目2目录**：`kafka-data-filter/`
3. **创建共享文档目录**：`docs/`

### 阶段2：移动项目1文件 (Kafka消费者组监控程序)

**移动到 `kafka-consumer-monitor/`：**
- `main.py` → `kafka-consumer-monitor/main.py`
- `kafka_consumer_monitor/` → `kafka-consumer-monitor/kafka_consumer_monitor/`
- `config.example.yaml` → `kafka-consumer-monitor/config.example.yaml`
- `reports/` → `kafka-consumer-monitor/reports/`
- `README.md` → `kafka-consumer-monitor/README.md`
- `debug_describe_groups.py` → `kafka-consumer-monitor/scripts/debug_describe_groups.py`

**创建项目1专用文件：**
- `kafka-consumer-monitor/requirements.txt` (从当前requirements.txt提取相关依赖)
- `kafka-consumer-monitor/logs/` (创建日志目录)

### 阶段3：移动项目2文件 (Kafka数据过滤程序)

**移动到 `kafka-data-filter/`：**
- `kafka_data_filter/` → `kafka-data-filter/kafka_data_filter/`
- `kafka_filter_main.py` → `kafka-data-filter/main.py`
- `filter_config.yaml` → `kafka-data-filter/filter_config.yaml`
- `example_config.yaml` → `kafka-data-filter/example_config.yaml`
- `tests/` → `kafka-data-filter/tests/`
- `run_tests.py` → `kafka-data-filter/run_tests.py`
- `test_filter_rules.py` → `kafka-data-filter/scripts/test_filter_rules.py`
- `test_run_methods.py` → `kafka-data-filter/scripts/test_run_methods.py`

**创建项目2专用文件：**
- `kafka-data-filter/README.md` (从KAFKA_FILTER_README.md重命名)
- `kafka-data-filter/requirements.txt` (从当前requirements.txt提取相关依赖)
- `kafka-data-filter/output/` (创建输出目录)
- `kafka-data-filter/logs/` (创建日志目录)
- `kafka-data-filter/scripts/` (创建脚本目录)

### 阶段4：处理共享和冲突文件

**移动到 `docs/`：**
- `PROJECT_SUMMARY.md` → `docs/PROJECT_OVERVIEW.md`
- 创建 `docs/DEVELOPMENT_GUIDE.md`

**需要删除的文件：**
- `KAFKA_FILTER_README.md` (已重命名移动)
- `filtered_messages.json` (移动到项目2输出目录)
- `kafka_data_filter.log` (移动到项目2日志目录)
- `kafka_monitor.log` (移动到项目1日志目录)

## 依赖分离方案

### 项目1依赖 (kafka-consumer-monitor/requirements.txt)
```
kafka-python-ng==2.2.2
PyYAML==6.0.1
colorama==0.4.6
tabulate==0.9.0
click==8.1.7
```

### 项目2依赖 (kafka-data-filter/requirements.txt)
```
kafka-python-ng==2.2.2
PyYAML==6.0.1
```

## 导入路径修正

### 项目1需要修正的导入：
- `main.py` 中的导入路径保持不变（相对导入）

### 项目2需要修正的导入：
- `kafka_data_filter/main.py` 中的导入路径保持不变
- 测试文件中的导入路径需要更新

## 配置文件分离

### 项目1配置：
- `kafka-consumer-monitor/config.example.yaml`

### 项目2配置：
- `kafka-data-filter/filter_config.yaml`
- `kafka-data-filter/example_config.yaml`

## 避免命名冲突

两个项目的模块名已经很好地分离：
- 项目1：`kafka_consumer_monitor`
- 项目2：`kafka_data_filter`

无需额外的命名冲突处理。

## 实施步骤

1. **备份当前项目**
2. **创建新目录结构**
3. **移动文件到对应目录**
4. **更新导入路径**
5. **创建独立的依赖文件**
6. **更新文档**
7. **测试两个项目的独立运行**

## 验证清单

- [ ] 项目1可以独立运行
- [ ] 项目2可以独立运行
- [ ] 两个项目的依赖互不冲突
- [ ] 导入路径正确
- [ ] 配置文件正确分离
- [ ] 文档完整且准确
- [ ] 测试可以正常运行
