#!/usr/bin/env python3
"""
Kafka消费者组监控程序主入口

提供命令行界面，启动和管理监控服务
"""

import sys
import signal
import argparse
import logging
from pathlib import Path

import click

from kafka_consumer_monitor.config.config_loader import ConfigLoader
from kafka_consumer_monitor.core.consumer_group_monitor import ConsumerGroupMonitor
from kafka_consumer_monitor.reporters.console_reporter import ConsoleReporter
from kafka_consumer_monitor.reporters.file_reporter import FileReporter
from kafka_consumer_monitor.reporters.alert_manager import <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>e<PERSON><PERSON>t<PERSON>and<PERSON>, FileAlertHandler
from kafka_consumer_monitor.utils.logger import setup_logging


class KafkaMonitorApp:
    """
    Kafka监控应用程序主类
    
    负责协调各个组件，管理应用程序生命周期
    """
    
    def __init__(self, config_path: str = None):
        """
        初始化应用程序
        
        参数:
            config_path: 配置文件路径
        """
        self.config_loader = ConfigLoader(config_path)
        self.monitor = None
        self.console_reporter = None
        self.file_reporter = None
        self.alert_manager = None
        self.is_running = False
        
        # 设置日志
        logging_config = self.config_loader.get_logging_config()
        setup_logging(logging_config)
        
        self.logger = logging.getLogger(__name__)
    
    def start(self):
        """
        启动监控应用程序
        """
        try:
            self.logger.info("正在启动Kafka消费者组监控程序...")
            
            # 初始化监控器
            self.monitor = ConsumerGroupMonitor(self.config_loader)
            
            # 初始化报告器
            self._setup_reporters()
            
            # 初始化告警管理器
            self._setup_alert_manager()
            
            # 设置回调函数
            self._setup_callbacks()
            
            # 启动各个组件
            self.monitor.start()
            
            if self.console_reporter:
                self.console_reporter.start()
            
            if self.file_reporter:
                self.file_reporter.start()
            
            self.is_running = True
            self.logger.info("Kafka消费者组监控程序启动成功")
            
            # 设置信号处理
            signal.signal(signal.SIGINT, self._signal_handler)
            signal.signal(signal.SIGTERM, self._signal_handler)
            
            return True
            
        except Exception as e:
            self.logger.error(f"启动监控程序失败: {e}")
            return False
    
    def stop(self):
        """
        停止监控应用程序
        """
        if not self.is_running:
            return
        
        self.logger.info("正在停止Kafka消费者组监控程序...")
        
        try:
            # 停止各个组件
            if self.monitor:
                self.monitor.stop()
            
            if self.console_reporter:
                self.console_reporter.stop()
            
            if self.file_reporter:
                self.file_reporter.stop()
            
            self.is_running = False
            self.logger.info("Kafka消费者组监控程序已停止")
            
        except Exception as e:
            self.logger.error(f"停止监控程序时出错: {e}")
    
    def _setup_reporters(self):
        """
        设置报告器
        """
        output_config = self.config_loader.get_output_config()
        
        # 控制台报告器
        console_config = output_config.get('console', {})
        if console_config.get('enabled', True):
            self.console_reporter = ConsoleReporter(console_config)
        
        # 文件报告器
        file_config = output_config.get('file', {})
        if file_config.get('enabled', True):
            self.file_reporter = FileReporter(file_config)
    
    def _setup_alert_manager(self):
        """
        设置告警管理器
        """
        alerts_config = self.config_loader.get_alerts_config()
        
        if alerts_config.get('enabled', True):
            self.alert_manager = AlertManager(alerts_config)
            
            # 添加控制台告警处理器
            console_handler = ConsoleAlertHandler()
            self.alert_manager.add_alert_handler(console_handler)
            
            # 添加文件告警处理器
            if self.file_reporter:
                output_dir = Path(self.config_loader.get('output.file.output_dir', './reports'))
                alert_file = output_dir / 'alerts.log'
                file_handler = FileAlertHandler(str(alert_file))
                self.alert_manager.add_alert_handler(file_handler)
    
    def _setup_callbacks(self):
        """
        设置回调函数
        """
        # 监控更新回调
        def on_groups_update(groups_data):
            if self.console_reporter:
                self.console_reporter.update_groups(groups_data)
            
            if self.file_reporter:
                self.file_reporter.update_groups(groups_data)
            
            # 更新统计信息
            stats = self.monitor.get_monitoring_stats()
            if self.console_reporter:
                self.console_reporter.update_stats(stats)
            
            if self.file_reporter:
                self.file_reporter.update_stats(stats)
        
        # 事件回调
        def on_event(event):
            if self.console_reporter:
                self.console_reporter.add_event(event)
            
            if self.file_reporter:
                self.file_reporter.log_event(event)
            
            if self.alert_manager:
                self.alert_manager.process_event(event)
        
        # 注册回调
        self.monitor.add_update_callback(on_groups_update)
        self.monitor.add_event_callback(on_event)
    
    def _signal_handler(self, signum, frame):
        """
        信号处理器
        
        参数:
            signum: 信号编号
            frame: 当前栈帧
        """
        self.logger.info(f"收到信号 {signum}，正在优雅关闭...")
        self.stop()
        sys.exit(0)
    
    def run_forever(self):
        """
        持续运行监控程序
        """
        if not self.start():
            sys.exit(1)
        
        try:
            # 主循环
            while self.is_running:
                import time
                time.sleep(1)
        except KeyboardInterrupt:
            self.logger.info("收到键盘中断，正在停止...")
        finally:
            self.stop()


@click.group()
@click.option('--config', '-c', help='配置文件路径')
@click.option('--verbose', '-v', is_flag=True, help='启用详细输出')
@click.pass_context
def cli(ctx, config, verbose):
    """
    Kafka消费者组监控程序
    
    监控Kafka集群中的消费者组状态和积压情况
    """
    ctx.ensure_object(dict)
    ctx.obj['config'] = config
    ctx.obj['verbose'] = verbose


@cli.command()
@click.pass_context
def start(ctx):
    """
    启动监控服务
    """
    config_path = ctx.obj.get('config')
    
    try:
        app = KafkaMonitorApp(config_path)
        app.run_forever()
    except Exception as e:
        click.echo(f"启动失败: {e}", err=True)
        sys.exit(1)


@cli.command()
@click.option('--group', '-g', help='指定消费者组ID')
@click.option('--format', '-f', type=click.Choice(['table', 'json']), default='table', help='输出格式')
@click.pass_context
def status(ctx, group, format):
    """
    查看消费者组状态
    """
    config_path = ctx.obj.get('config')
    
    try:
        app = KafkaMonitorApp(config_path)
        
        # 临时启动监控器获取状态
        if app.start():
            import time
            time.sleep(2)  # 等待数据收集
            
            groups_data = app.monitor.get_current_groups()
            
            if group:
                if group in groups_data:
                    group_info = groups_data[group]
                    if format == 'json':
                        import json
                        print(json.dumps(group_info['consumer_group'].to_dict(), 
                                       ensure_ascii=False, indent=2))
                    else:
                        _print_group_status(group_info)
                else:
                    click.echo(f"消费者组 '{group}' 不存在")
            else:
                if format == 'json':
                    import json
                    result = {gid: info['consumer_group'].to_dict() 
                             for gid, info in groups_data.items()}
                    print(json.dumps(result, ensure_ascii=False, indent=2))
                else:
                    _print_all_groups_status(groups_data)
            
            app.stop()
        else:
            click.echo("无法连接到Kafka集群", err=True)
            sys.exit(1)
            
    except Exception as e:
        click.echo(f"查看状态失败: {e}", err=True)
        sys.exit(1)


@cli.command()
@click.option('--hours', '-h', type=int, default=1, help='查看最近N小时的事件')
@click.option('--group', '-g', help='指定消费者组ID')
@click.pass_context
def events(ctx, hours, group):
    """
    查看最近事件
    """
    config_path = ctx.obj.get('config')
    
    try:
        app = KafkaMonitorApp(config_path)
        
        if app.start():
            import time
            time.sleep(2)  # 等待数据收集
            
            recent_events = app.monitor.get_recent_events(group, hours)
            
            if recent_events:
                click.echo(f"最近 {hours} 小时的事件:")
                for event in recent_events:
                    timestamp = event.timestamp.strftime('%Y-%m-%d %H:%M:%S')
                    click.echo(f"[{timestamp}] {event.group_id}: {event.message}")
            else:
                click.echo("没有找到相关事件")
            
            app.stop()
        else:
            click.echo("无法连接到Kafka集群", err=True)
            sys.exit(1)
            
    except Exception as e:
        click.echo(f"查看事件失败: {e}", err=True)
        sys.exit(1)


def _print_group_status(group_info):
    """
    打印单个消费者组状态
    
    参数:
        group_info: 消费者组信息
    """
    consumer_group = group_info['consumer_group']
    lag_stats = group_info['lag_stats']
    
    click.echo(f"消费者组: {consumer_group.group_id}")
    click.echo(f"状态: {consumer_group.state.value}")
    click.echo(f"成员数: {consumer_group.member_count}")
    click.echo(f"主题: {', '.join(consumer_group.topics)}")
    click.echo(f"总积压: {lag_stats['total_lag']:,}")
    click.echo(f"有积压分区: {lag_stats['lagging_partitions_count']}/{lag_stats['total_partitions']}")


def _print_all_groups_status(groups_data):
    """
    打印所有消费者组状态
    
    参数:
        groups_data: 消费者组数据
    """
    from kafka_consumer_monitor.utils.formatter import format_consumer_group_table
    
    if groups_data:
        table = format_consumer_group_table(groups_data)
        click.echo(table)
    else:
        click.echo("没有找到消费者组")


if __name__ == '__main__':
    cli()
