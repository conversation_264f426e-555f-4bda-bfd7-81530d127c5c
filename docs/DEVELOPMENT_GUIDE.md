# 开发指南

本文档提供PythonProject中两个独立项目的开发指南。

## 项目结构

PythonProject包含两个独立的Python项目：

### 1. Kafka消费者组监控程序 (kafka-consumer-monitor)
- **目录**: `kafka-consumer-monitor/`
- **功能**: 监控Kafka集群中的消费者组状态、积压情况、事件跟踪
- **主要技术**: kafka-python-ng, PyYAML, colorama, tabulate, click

### 2. Kafka数据过滤程序 (kafka-data-filter)
- **目录**: `kafka-data-filter/`
- **功能**: 从Kafka消费JSON消息并根据复杂规则进行过滤
- **主要技术**: kafka-python-ng, PyYAML

## 开发环境设置

### 项目1：Kafka消费者组监控程序

```bash
cd kafka-consumer-monitor
pip install -r requirements.txt
```

### 项目2：Kafka数据过滤程序

```bash
cd kafka-data-filter
pip install -r requirements.txt
```

## 运行项目

### 项目1：Kafka消费者组监控程序

```bash
cd kafka-consumer-monitor
python main.py --config config.example.yaml
```

### 项目2：Kafka数据过滤程序

```bash
cd kafka-data-filter

# 方法1：使用启动脚本
python main.py filter_config.yaml

# 方法2：直接运行核心模块
python kafka_data_filter/main.py filter_config.yaml
```

## 测试

### 项目1测试
```bash
cd kafka-consumer-monitor
# 项目1暂无单元测试，可以运行调试脚本
python scripts/debug_describe_groups.py
```

### 项目2测试
```bash
cd kafka-data-filter

# 运行所有单元测试
python run_tests.py

# 运行特定测试模块
python run_tests.py --module test_filter_parser

# 运行功能测试脚本
python scripts/test_filter_rules.py
python scripts/test_run_methods.py
```

## 项目独立性

两个项目完全独立，具有：

1. **独立的依赖管理**: 各自的 `requirements.txt`
2. **独立的配置文件**: 不同的配置文件格式和内容
3. **独立的模块命名**: `kafka_consumer_monitor` vs `kafka_data_filter`
4. **独立的日志和输出**: 各自的 `logs/` 和 `output/` 目录
5. **独立的文档**: 各自的 `README.md`

## 开发最佳实践

### 1. 模块导入
- 项目1: 使用相对导入 `from kafka_consumer_monitor.xxx import yyy`
- 项目2: 使用相对导入 `from kafka_data_filter.xxx import yyy`

### 2. 配置管理
- 项目1: 使用YAML配置，通过ConfigLoader加载
- 项目2: 使用YAML配置，通过ConfigManager加载

### 3. 日志管理
- 项目1: 日志文件存储在 `kafka-consumer-monitor/logs/`
- 项目2: 日志文件存储在 `kafka-data-filter/logs/`

### 4. 测试
- 项目1: 主要通过手动测试和调试脚本
- 项目2: 完整的单元测试套件

## 部署建议

### 生产环境部署

每个项目可以独立部署：

```bash
# 项目1部署
cd kafka-consumer-monitor
pip install -r requirements.txt
python main.py --config production_config.yaml

# 项目2部署
cd kafka-data-filter
pip install -r requirements.txt
python main.py production_filter_config.yaml
```

### Docker化部署

可以为每个项目创建独立的Dockerfile：

```dockerfile
# 项目1 Dockerfile
FROM python:3.9-slim
WORKDIR /app
COPY kafka-consumer-monitor/ .
RUN pip install -r requirements.txt
CMD ["python", "main.py", "--config", "config.yaml"]

# 项目2 Dockerfile
FROM python:3.9-slim
WORKDIR /app
COPY kafka-data-filter/ .
RUN pip install -r requirements.txt
CMD ["python", "main.py", "filter_config.yaml"]
```

## 故障排除

### 常见问题

1. **导入错误**: 确保在正确的项目目录中运行命令
2. **依赖冲突**: 使用虚拟环境隔离每个项目的依赖
3. **配置文件路径**: 确保配置文件路径相对于项目根目录

### 调试技巧

1. **启用详细日志**: 在配置文件中设置日志级别为DEBUG
2. **使用测试脚本**: 项目2提供了丰富的测试脚本
3. **检查Kafka连接**: 确保Kafka集群可访问

## 贡献指南

1. **代码风格**: 遵循PEP 8规范
2. **注释**: 使用中文注释，详细说明功能
3. **测试**: 为新功能添加相应的测试
4. **文档**: 更新相关的README和文档

## 版本管理

建议为每个项目独立管理版本：

- 项目1: kafka-consumer-monitor v1.0.0
- 项目2: kafka-data-filter v1.0.0

## 联系信息

如有问题或建议，请查看各项目的README文档或提交Issue。
