2025-08-21 17:46:38,501 - __main__ - INFO - 日志系统已配置，级别: INFO
2025-08-21 17:46:38,501 - __main__ - INFO - 开始初始化Kafka数据过滤程序
2025-08-21 17:46:38,501 - kafka_data_filter.filter_parser - ERROR - 解析表达式失败: "y_is_first_gather": 1, 错误: 期望字段名，但得到: y_is_first_gather
2025-08-21 17:46:38,501 - kafka_data_filter.data_filter - ERROR - 解析规则 0 失败: "y_is_first_gather": 1, 错误: 表达式解析错误: 期望字段名，但得到: y_is_first_gather
2025-08-21 17:46:38,501 - __main__ - ERROR - 组件初始化失败: 过滤规则解析失败: 表达式解析错误: 期望字段名，但得到: y_is_first_gather
2025-08-21 17:46:59,361 - __main__ - INFO - 日志系统已配置，级别: INFO
2025-08-21 17:46:59,361 - __main__ - INFO - 开始初始化Kafka数据过滤程序
2025-08-21 17:46:59,361 - kafka_data_filter.data_filter - INFO - 成功解析 2 个过滤规则
2025-08-21 17:46:59,361 - __main__ - INFO - 数据过滤器已初始化，规则数量: 2
2025-08-21 17:46:59,361 - __main__ - INFO - 输出处理器已初始化
2025-08-21 17:46:59,361 - kafka_data_filter.statistics - INFO - 统计模块已初始化，启用状态: True
2025-08-21 17:46:59,361 - __main__ - INFO - 统计模块已初始化
2025-08-21 17:46:59,361 - __main__ - INFO - Kafka消费者已初始化
2025-08-21 17:46:59,361 - __main__ - INFO - 所有组件初始化完成
2025-08-21 17:46:59,361 - __main__ - INFO - 启动Kafka数据过滤程序
2025-08-21 17:46:59,361 - kafka_data_filter.statistics - INFO - 统计已开始
2025-08-21 17:46:59,366 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=localhost:9092 <connecting> [IPv6 ('::1', 9092, 0, 0)]>: connecting to localhost:9092 [('::1', 9092, 0, 0) IPv6]
2025-08-21 17:46:59,366 - kafka.conn - INFO - Probing node bootstrap-0 broker version
2025-08-21 17:46:59,367 - kafka.conn - ERROR - Connect attempt to <BrokerConnection node_id=bootstrap-0 host=localhost:9092 <connecting> [IPv6 ('::1', 9092, 0, 0)]> returned error 61. Disconnecting.
2025-08-21 17:46:59,367 - kafka.conn - ERROR - <BrokerConnection node_id=bootstrap-0 host=localhost:9092 <connecting> [IPv6 ('::1', 9092, 0, 0)]>: Closing connection. KafkaConnectionError: 61 ECONNREFUSED
2025-08-21 17:46:59,416 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=localhost:9092 <connecting> [IPv6 ('::1', 9092, 0, 0)]>: connecting to localhost:9092 [('::1', 9092, 0, 0) IPv6]
2025-08-21 17:46:59,417 - kafka.conn - ERROR - Connect attempt to <BrokerConnection node_id=bootstrap-0 host=localhost:9092 <connecting> [IPv6 ('::1', 9092, 0, 0)]> returned error 61. Disconnecting.
2025-08-21 17:46:59,417 - kafka.conn - ERROR - <BrokerConnection node_id=bootstrap-0 host=localhost:9092 <connecting> [IPv6 ('::1', 9092, 0, 0)]>: Closing connection. KafkaConnectionError: 61 ECONNREFUSED
2025-08-21 17:46:59,466 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=localhost:9092 <connecting> [IPv4 ('127.0.0.1', 9092)]>: connecting to localhost:9092 [('127.0.0.1', 9092) IPv4]
2025-08-21 17:46:59,467 - kafka.conn - ERROR - Connect attempt to <BrokerConnection node_id=bootstrap-0 host=localhost:9092 <connecting> [IPv4 ('127.0.0.1', 9092)]> returned error 61. Disconnecting.
2025-08-21 17:46:59,467 - kafka.conn - ERROR - <BrokerConnection node_id=bootstrap-0 host=localhost:9092 <connecting> [IPv4 ('127.0.0.1', 9092)]>: Closing connection. KafkaConnectionError: 61 ECONNREFUSED
2025-08-21 17:46:59,467 - kafka_data_filter.kafka_consumer - ERROR - 连接Kafka失败: NoBrokersAvailable
2025-08-21 17:46:59,467 - __main__ - ERROR - 程序运行失败: Kafka连接失败: NoBrokersAvailable
2025-08-21 17:46:59,468 - __main__ - INFO - 开始清理资源
2025-08-21 17:46:59,468 - __main__ - INFO - 最终统计信息:
2025-08-21 17:46:59,468 - __main__ - INFO - 运行时间: 0.11 秒
消费消息数: 0
处理消息数: 0
通过过滤数: 0
被过滤数: 0
输出消息数: 0
错误数: 0
通过率: 0%
平均处理时间: 0 ms
当前吞吐量: 0.0 消息/秒
总体吞吐量: 0.0 消息/秒
2025-08-21 17:46:59,468 - __main__ - INFO - Kafka消费者已关闭
2025-08-21 17:46:59,468 - __main__ - INFO - 输出处理器已关闭
2025-08-21 17:46:59,468 - __main__ - INFO - 资源清理完成
2025-08-21 17:47:12,688 - __main__ - INFO - 日志系统已配置，级别: INFO
2025-08-21 17:47:12,689 - __main__ - INFO - 开始初始化Kafka数据过滤程序
2025-08-21 17:47:12,689 - kafka_data_filter.data_filter - INFO - 成功解析 2 个过滤规则
2025-08-21 17:47:12,689 - __main__ - INFO - 数据过滤器已初始化，规则数量: 2
2025-08-21 17:47:12,689 - __main__ - INFO - 输出处理器已初始化
2025-08-21 17:47:12,689 - kafka_data_filter.statistics - INFO - 统计模块已初始化，启用状态: True
2025-08-21 17:47:12,689 - __main__ - INFO - 统计模块已初始化
2025-08-21 17:47:12,689 - __main__ - INFO - Kafka消费者已初始化
2025-08-21 17:47:12,689 - __main__ - INFO - 所有组件初始化完成
2025-08-21 17:47:12,689 - __main__ - INFO - 启动Kafka数据过滤程序
2025-08-21 17:47:12,689 - kafka_data_filter.statistics - INFO - 统计已开始
2025-08-21 17:47:12,693 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=localhost:9092 <connecting> [IPv6 ('::1', 9092, 0, 0)]>: connecting to localhost:9092 [('::1', 9092, 0, 0) IPv6]
2025-08-21 17:47:12,693 - kafka.conn - INFO - Probing node bootstrap-0 broker version
2025-08-21 17:47:12,694 - kafka.conn - ERROR - Connect attempt to <BrokerConnection node_id=bootstrap-0 host=localhost:9092 <connecting> [IPv6 ('::1', 9092, 0, 0)]> returned error 61. Disconnecting.
2025-08-21 17:47:12,694 - kafka.conn - ERROR - <BrokerConnection node_id=bootstrap-0 host=localhost:9092 <connecting> [IPv6 ('::1', 9092, 0, 0)]>: Closing connection. KafkaConnectionError: 61 ECONNREFUSED
2025-08-21 17:47:12,743 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=localhost:9092 <connecting> [IPv6 ('::1', 9092, 0, 0)]>: connecting to localhost:9092 [('::1', 9092, 0, 0) IPv6]
2025-08-21 17:47:12,744 - kafka.conn - ERROR - Connect attempt to <BrokerConnection node_id=bootstrap-0 host=localhost:9092 <connecting> [IPv6 ('::1', 9092, 0, 0)]> returned error 61. Disconnecting.
2025-08-21 17:47:12,744 - kafka.conn - ERROR - <BrokerConnection node_id=bootstrap-0 host=localhost:9092 <connecting> [IPv6 ('::1', 9092, 0, 0)]>: Closing connection. KafkaConnectionError: 61 ECONNREFUSED
2025-08-21 17:47:12,793 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=localhost:9092 <connecting> [IPv4 ('127.0.0.1', 9092)]>: connecting to localhost:9092 [('127.0.0.1', 9092) IPv4]
2025-08-21 17:47:12,794 - kafka.conn - ERROR - Connect attempt to <BrokerConnection node_id=bootstrap-0 host=localhost:9092 <connecting> [IPv4 ('127.0.0.1', 9092)]> returned error 61. Disconnecting.
2025-08-21 17:47:12,794 - kafka.conn - ERROR - <BrokerConnection node_id=bootstrap-0 host=localhost:9092 <connecting> [IPv4 ('127.0.0.1', 9092)]>: Closing connection. KafkaConnectionError: 61 ECONNREFUSED
2025-08-21 17:47:12,794 - kafka_data_filter.kafka_consumer - ERROR - 连接Kafka失败: NoBrokersAvailable
2025-08-21 17:47:12,795 - __main__ - ERROR - 程序运行失败: Kafka连接失败: NoBrokersAvailable
2025-08-21 17:47:12,795 - __main__ - INFO - 开始清理资源
2025-08-21 17:47:12,795 - __main__ - INFO - 最终统计信息:
2025-08-21 17:47:12,795 - __main__ - INFO - 运行时间: 0.11 秒
消费消息数: 0
处理消息数: 0
通过过滤数: 0
被过滤数: 0
输出消息数: 0
错误数: 0
通过率: 0%
平均处理时间: 0 ms
当前吞吐量: 0.0 消息/秒
总体吞吐量: 0.0 消息/秒
2025-08-21 17:47:12,795 - __main__ - INFO - Kafka消费者已关闭
2025-08-21 17:47:12,795 - __main__ - INFO - 输出处理器已关闭
2025-08-21 17:47:12,795 - __main__ - INFO - 资源清理完成
2025-08-21 17:49:38,082 - __main__ - INFO - 日志系统已配置，级别: INFO
2025-08-21 17:49:38,082 - __main__ - INFO - 开始初始化Kafka数据过滤程序
2025-08-21 17:49:38,082 - kafka_data_filter.data_filter - INFO - 成功解析 1 个过滤规则
2025-08-21 17:49:38,082 - __main__ - INFO - 数据过滤器已初始化，规则数量: 1
2025-08-21 17:49:38,082 - __main__ - INFO - 输出处理器已初始化
2025-08-21 17:49:38,082 - kafka_data_filter.statistics - INFO - 统计模块已初始化，启用状态: True
2025-08-21 17:49:38,082 - __main__ - INFO - 统计模块已初始化
2025-08-21 17:49:38,082 - __main__ - INFO - Kafka消费者已初始化
2025-08-21 17:49:38,082 - __main__ - INFO - 所有组件初始化完成
2025-08-21 17:49:38,082 - __main__ - INFO - 启动Kafka数据过滤程序
2025-08-21 17:49:38,082 - kafka_data_filter.statistics - INFO - 统计已开始
2025-08-21 17:49:38,087 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: connecting to **************:19301 [('**************', 19301) IPv4]
2025-08-21 17:49:38,089 - kafka.conn - INFO - Probing node bootstrap-0 broker version
2025-08-21 17:49:38,156 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: Connection complete.
2025-08-21 17:49:38,333 - kafka.conn - INFO - Broker version identified as 2.6.0
2025-08-21 17:49:38,334 - kafka.conn - INFO - Set configuration api_version=(2, 6, 0) to skip auto check_version requests on startup
2025-08-21 17:49:38,334 - kafka.consumer.subscription_state - INFO - Updating subscribed topics to: ('moyeT_dfycb_website',)
2025-08-21 17:49:38,334 - kafka_data_filter.kafka_consumer - INFO - 成功连接到Kafka集群，订阅topic: moyeT_dfycb_website
2025-08-21 17:49:38,334 - kafka_data_filter.kafka_consumer - INFO - 消费者配置: {'bootstrap_servers': '**************:19301', 'group_id': 'moye-check-data', 'auto_offset_reset': 'latest', 'enable_auto_commit': True, 'value_deserializer': <function ConfigManager.get_consumer_config.<locals>.<lambda> at 0x103edb1a0>}
2025-08-21 17:49:38,334 - __main__ - INFO - 开始消费topic: moyeT_dfycb_website
2025-08-21 17:49:38,334 - kafka_data_filter.kafka_consumer - INFO - 开始消费消息，最大消息数: 无限制
2025-08-21 17:49:38,404 - kafka.cluster - INFO - Group coordinator for moye-check-data is BrokerMetadata(nodeId='coordinator-0', host='**************', port=19301, rack=None)
2025-08-21 17:49:38,404 - kafka.coordinator - INFO - Discovered coordinator coordinator-0 for group moye-check-data
2025-08-21 17:49:38,404 - kafka.coordinator - INFO - Starting new heartbeat thread
2025-08-21 17:49:38,405 - kafka.coordinator.consumer - INFO - Revoking previously assigned partitions set() for group moye-check-data
2025-08-21 17:49:38,405 - kafka.conn - INFO - <BrokerConnection node_id=coordinator-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: connecting to **************:19301 [('**************', 19301) IPv4]
2025-08-21 17:49:38,511 - kafka.conn - INFO - <BrokerConnection node_id=coordinator-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: Connection complete.
2025-08-21 17:49:38,512 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connected> [IPv4 ('**************', 19301)]>: Closing connection. 
2025-08-21 17:49:38,617 - kafka.coordinator - INFO - (Re-)joining group moye-check-data
2025-08-21 17:49:38,718 - kafka.coordinator - INFO - Elected group leader -- performing partition assignments using range
2025-08-21 17:49:38,720 - kafka.conn - INFO - <BrokerConnection node_id=0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: connecting to **************:19301 [('**************', 19301) IPv4]
2025-08-21 17:49:38,791 - kafka.conn - INFO - <BrokerConnection node_id=0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: Connection complete.
2025-08-21 17:49:38,793 - kafka.coordinator - INFO - Successfully joined group moye-check-data with generation 1
2025-08-21 17:49:38,793 - kafka.consumer.subscription_state - INFO - Updated partition assignment: [TopicPartition(topic='moyeT_dfycb_website', partition=0)]
2025-08-21 17:49:38,793 - kafka.coordinator.consumer - INFO - Setting newly assigned partitions {TopicPartition(topic='moyeT_dfycb_website', partition=0)} for group moye-check-data
2025-08-21 17:49:55,119 - __main__ - INFO - 收到信号 SIGINT，准备停止程序
2025-08-21 17:49:55,120 - __main__ - INFO - 正在停止应用程序...
2025-08-21 17:49:55,120 - kafka_data_filter.kafka_consumer - INFO - 停止消费消息
2025-08-21 17:49:55,154 - kafka_data_filter.kafka_consumer - INFO - 消费结束，总处理消息数: 11, 错误数: 0
2025-08-21 17:49:55,154 - __main__ - INFO - 开始清理资源
2025-08-21 17:49:55,154 - __main__ - INFO - 最终统计信息:
2025-08-21 17:49:55,154 - __main__ - INFO - 运行时间: 17.07 秒
消费消息数: 11
处理消息数: 11
通过过滤数: 4
被过滤数: 7
输出消息数: 4
错误数: 0
通过率: 36.36%
平均处理时间: 0.02 ms
当前吞吐量: 0.94 消息/秒
总体吞吐量: 0.64 消息/秒
2025-08-21 17:49:55,238 - kafka.coordinator - INFO - Stopping heartbeat thread
2025-08-21 17:49:55,238 - kafka.coordinator - INFO - Leaving consumer group (moye-check-data).
2025-08-21 17:49:55,320 - kafka.conn - INFO - <BrokerConnection node_id=coordinator-0 host=**************:19301 <connected> [IPv4 ('**************', 19301)]>: Closing connection. 
2025-08-21 17:49:55,321 - kafka.conn - INFO - <BrokerConnection node_id=0 host=**************:19301 <connected> [IPv4 ('**************', 19301)]>: Closing connection. 
2025-08-21 17:49:55,321 - kafka.consumer.fetcher - ERROR - Fetch to node 0 failed: Cancelled: <BrokerConnection node_id=0 host=**************:19301 <connected> [IPv4 ('**************', 19301)]>
2025-08-21 17:49:55,321 - kafka_data_filter.kafka_consumer - INFO - Kafka消费者连接已关闭
2025-08-21 17:49:55,321 - __main__ - INFO - Kafka消费者已关闭
2025-08-21 17:49:55,321 - __main__ - INFO - 输出处理器已关闭
2025-08-21 17:49:55,321 - __main__ - INFO - 资源清理完成
2025-08-21 17:56:10,602 - __main__ - INFO - 日志系统已配置，级别: INFO
2025-08-21 17:56:10,602 - __main__ - INFO - 开始初始化Kafka数据过滤程序
2025-08-21 17:56:10,602 - kafka_data_filter.filter_parser - ERROR - 解析表达式失败: y_is_first_gather = 1 AND n_content_spam.class2 NOT IN ("广告信息", "股票资讯", "新闻合集", "历史科普", "明星娱乐") AND y_yq_focus.label_name = "关注" AND n_media.nature IN ("境外媒体", "党媒","政府部门"), 错误: 期望运算符，但得到: NOT
2025-08-21 17:56:10,602 - kafka_data_filter.data_filter - ERROR - 解析规则 0 失败: y_is_first_gather = 1 AND n_content_spam.class2 NOT IN ("广告信息", "股票资讯", "新闻合集", "历史科普", "明星娱乐") AND y_yq_focus.label_name = "关注" AND n_media.nature IN ("境外媒体", "党媒","政府部门"), 错误: 表达式解析错误: 期望运算符，但得到: NOT
2025-08-21 17:56:10,602 - __main__ - ERROR - 组件初始化失败: 过滤规则解析失败: 表达式解析错误: 期望运算符，但得到: NOT
2025-08-21 17:58:41,601 - __main__ - INFO - 日志系统已配置，级别: INFO
2025-08-21 17:58:41,601 - __main__ - INFO - 开始初始化Kafka数据过滤程序
2025-08-21 17:58:41,601 - kafka_data_filter.data_filter - INFO - 成功解析 2 个过滤规则
2025-08-21 17:58:41,601 - __main__ - INFO - 数据过滤器已初始化，规则数量: 2
2025-08-21 17:58:41,602 - kafka_data_filter.output_handler - INFO - 文件输出已初始化: filtered_messages.json
2025-08-21 17:58:41,602 - __main__ - INFO - 输出处理器已初始化
2025-08-21 17:58:41,602 - kafka_data_filter.statistics - INFO - 统计模块已初始化，启用状态: True
2025-08-21 17:58:41,602 - __main__ - INFO - 统计模块已初始化
2025-08-21 17:58:41,602 - __main__ - INFO - Kafka消费者已初始化
2025-08-21 17:58:41,602 - __main__ - INFO - 所有组件初始化完成
2025-08-21 17:58:41,602 - __main__ - INFO - 启动Kafka数据过滤程序
2025-08-21 17:58:41,602 - kafka_data_filter.statistics - INFO - 统计已开始
2025-08-21 17:58:41,607 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: connecting to **************:19301 [('**************', 19301) IPv4]
2025-08-21 17:58:41,607 - kafka.conn - INFO - Probing node bootstrap-0 broker version
2025-08-21 17:58:41,683 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: Connection complete.
2025-08-21 17:58:41,862 - kafka.conn - INFO - Broker version identified as 2.6.0
2025-08-21 17:58:41,862 - kafka.conn - INFO - Set configuration api_version=(2, 6, 0) to skip auto check_version requests on startup
2025-08-21 17:58:41,862 - kafka.consumer.subscription_state - INFO - Updating subscribed topics to: ('moyeT_dfycb_website',)
2025-08-21 17:58:41,862 - kafka_data_filter.kafka_consumer - INFO - 成功连接到Kafka集群，订阅topic: moyeT_dfycb_website
2025-08-21 17:58:41,862 - kafka_data_filter.kafka_consumer - INFO - 消费者配置: {'bootstrap_servers': '**************:19301', 'group_id': 'moye-check-data', 'auto_offset_reset': 'latest', 'enable_auto_commit': True, 'value_deserializer': <function ConfigManager.get_consumer_config.<locals>.<lambda> at 0x10340b240>}
2025-08-21 17:58:41,862 - __main__ - INFO - 开始消费topic: moyeT_dfycb_website
2025-08-21 17:58:41,862 - kafka_data_filter.kafka_consumer - INFO - 开始消费消息，最大消息数: 5
2025-08-21 17:58:41,936 - kafka.cluster - INFO - Group coordinator for moye-check-data is BrokerMetadata(nodeId='coordinator-0', host='**************', port=19301, rack=None)
2025-08-21 17:58:41,936 - kafka.coordinator - INFO - Discovered coordinator coordinator-0 for group moye-check-data
2025-08-21 17:58:41,936 - kafka.coordinator - INFO - Starting new heartbeat thread
2025-08-21 17:58:41,936 - kafka.coordinator.consumer - INFO - Revoking previously assigned partitions set() for group moye-check-data
2025-08-21 17:58:41,936 - kafka.conn - INFO - <BrokerConnection node_id=coordinator-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: connecting to **************:19301 [('**************', 19301) IPv4]
2025-08-21 17:58:42,042 - kafka.conn - INFO - <BrokerConnection node_id=coordinator-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: Connection complete.
2025-08-21 17:58:42,042 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connected> [IPv4 ('**************', 19301)]>: Closing connection. 
2025-08-21 17:58:42,147 - kafka.coordinator - INFO - (Re-)joining group moye-check-data
2025-08-21 17:58:42,216 - kafka.coordinator - INFO - Elected group leader -- performing partition assignments using range
2025-08-21 17:58:42,217 - kafka.conn - INFO - <BrokerConnection node_id=0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: connecting to **************:19301 [('**************', 19301) IPv4]
2025-08-21 17:58:42,284 - kafka.coordinator - INFO - Successfully joined group moye-check-data with generation 3
2025-08-21 17:58:42,285 - kafka.consumer.subscription_state - INFO - Updated partition assignment: [TopicPartition(topic='moyeT_dfycb_website', partition=0)]
2025-08-21 17:58:42,285 - kafka.coordinator.consumer - INFO - Setting newly assigned partitions {TopicPartition(topic='moyeT_dfycb_website', partition=0)} for group moye-check-data
2025-08-21 17:58:42,388 - kafka.conn - INFO - <BrokerConnection node_id=0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: Connection complete.
2025-08-21 17:58:57,518 - kafka_data_filter.kafka_consumer - INFO - 已达到最大消息数限制: 5
2025-08-21 17:58:57,518 - kafka_data_filter.kafka_consumer - INFO - 消费结束，总处理消息数: 45, 错误数: 0
2025-08-21 17:58:57,518 - __main__ - INFO - 开始清理资源
2025-08-21 17:58:57,518 - __main__ - INFO - 最终统计信息:
2025-08-21 17:58:57,518 - __main__ - INFO - 运行时间: 15.92 秒
消费消息数: 45
处理消息数: 45
通过过滤数: 0
被过滤数: 45
输出消息数: 0
错误数: 0
通过率: 0.0%
平均处理时间: 0.03 ms
当前吞吐量: 6745.62 消息/秒
总体吞吐量: 2.83 消息/秒
2025-08-21 17:58:57,586 - kafka.coordinator - INFO - Stopping heartbeat thread
2025-08-21 17:58:57,586 - kafka.coordinator - INFO - Leaving consumer group (moye-check-data).
2025-08-21 17:58:57,918 - kafka.conn - INFO - <BrokerConnection node_id=coordinator-0 host=**************:19301 <connected> [IPv4 ('**************', 19301)]>: Closing connection. 
2025-08-21 17:58:57,918 - kafka.conn - INFO - <BrokerConnection node_id=0 host=**************:19301 <connected> [IPv4 ('**************', 19301)]>: Closing connection. 
2025-08-21 17:58:57,919 - kafka_data_filter.kafka_consumer - INFO - Kafka消费者连接已关闭
2025-08-21 17:58:57,919 - __main__ - INFO - Kafka消费者已关闭
2025-08-21 17:58:57,919 - kafka_data_filter.output_handler - INFO - 文件输出已关闭
2025-08-21 17:58:57,919 - __main__ - INFO - 输出处理器已关闭
2025-08-21 17:58:57,919 - __main__ - INFO - 资源清理完成
2025-08-21 17:59:40,710 - __main__ - INFO - 日志系统已配置，级别: INFO
2025-08-21 17:59:40,710 - __main__ - INFO - 开始初始化Kafka数据过滤程序
2025-08-21 17:59:40,710 - kafka_data_filter.data_filter - INFO - 成功解析 2 个过滤规则
2025-08-21 17:59:40,710 - __main__ - INFO - 数据过滤器已初始化，规则数量: 2
2025-08-21 17:59:40,710 - __main__ - INFO - 输出处理器已初始化
2025-08-21 17:59:40,710 - kafka_data_filter.statistics - INFO - 统计模块已初始化，启用状态: True
2025-08-21 17:59:40,710 - __main__ - INFO - 统计模块已初始化
2025-08-21 17:59:40,710 - __main__ - INFO - Kafka消费者已初始化
2025-08-21 17:59:40,710 - __main__ - INFO - 所有组件初始化完成
2025-08-21 17:59:40,710 - __main__ - INFO - 启动Kafka数据过滤程序
2025-08-21 17:59:40,710 - kafka_data_filter.statistics - INFO - 统计已开始
2025-08-21 17:59:40,714 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=localhost:9092 <connecting> [IPv6 ('::1', 9092, 0, 0)]>: connecting to localhost:9092 [('::1', 9092, 0, 0) IPv6]
2025-08-21 17:59:40,714 - kafka.conn - INFO - Probing node bootstrap-0 broker version
2025-08-21 17:59:40,716 - kafka.conn - ERROR - Connect attempt to <BrokerConnection node_id=bootstrap-0 host=localhost:9092 <connecting> [IPv6 ('::1', 9092, 0, 0)]> returned error 61. Disconnecting.
2025-08-21 17:59:40,716 - kafka.conn - ERROR - <BrokerConnection node_id=bootstrap-0 host=localhost:9092 <connecting> [IPv6 ('::1', 9092, 0, 0)]>: Closing connection. KafkaConnectionError: 61 ECONNREFUSED
2025-08-21 17:59:40,764 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=localhost:9092 <connecting> [IPv6 ('::1', 9092, 0, 0)]>: connecting to localhost:9092 [('::1', 9092, 0, 0) IPv6]
2025-08-21 17:59:40,767 - kafka.conn - ERROR - Connect attempt to <BrokerConnection node_id=bootstrap-0 host=localhost:9092 <connecting> [IPv6 ('::1', 9092, 0, 0)]> returned error 61. Disconnecting.
2025-08-21 17:59:40,767 - kafka.conn - ERROR - <BrokerConnection node_id=bootstrap-0 host=localhost:9092 <connecting> [IPv6 ('::1', 9092, 0, 0)]>: Closing connection. KafkaConnectionError: 61 ECONNREFUSED
2025-08-21 17:59:40,814 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=localhost:9092 <connecting> [IPv4 ('127.0.0.1', 9092)]>: connecting to localhost:9092 [('127.0.0.1', 9092) IPv4]
2025-08-21 17:59:40,815 - kafka.conn - ERROR - Connect attempt to <BrokerConnection node_id=bootstrap-0 host=localhost:9092 <connecting> [IPv4 ('127.0.0.1', 9092)]> returned error 61. Disconnecting.
2025-08-21 17:59:40,815 - kafka.conn - ERROR - <BrokerConnection node_id=bootstrap-0 host=localhost:9092 <connecting> [IPv4 ('127.0.0.1', 9092)]>: Closing connection. KafkaConnectionError: 61 ECONNREFUSED
2025-08-21 17:59:40,816 - kafka_data_filter.kafka_consumer - ERROR - 连接Kafka失败: NoBrokersAvailable
2025-08-21 17:59:40,816 - __main__ - ERROR - 程序运行失败: Kafka连接失败: NoBrokersAvailable
2025-08-21 17:59:40,816 - __main__ - INFO - 开始清理资源
2025-08-21 17:59:40,816 - __main__ - INFO - 最终统计信息:
2025-08-21 17:59:40,816 - __main__ - INFO - 运行时间: 0.11 秒
消费消息数: 0
处理消息数: 0
通过过滤数: 0
被过滤数: 0
输出消息数: 0
错误数: 0
通过率: 0%
平均处理时间: 0 ms
当前吞吐量: 0.0 消息/秒
总体吞吐量: 0.0 消息/秒
2025-08-21 17:59:40,816 - __main__ - INFO - Kafka消费者已关闭
2025-08-21 17:59:40,816 - __main__ - INFO - 输出处理器已关闭
2025-08-21 17:59:40,816 - __main__ - INFO - 资源清理完成
