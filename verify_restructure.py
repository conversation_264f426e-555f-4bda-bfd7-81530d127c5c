#!/usr/bin/env python3
"""
验证重构结果脚本

验证两个项目是否可以独立运行，依赖是否正确分离
"""

import subprocess
import sys
import os
from pathlib import Path


def run_command(command, cwd=None, timeout=10):
    """运行命令并返回结果"""
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            capture_output=True, 
            text=True, 
            timeout=timeout,
            cwd=cwd
        )
        return result.returncode == 0, result.stdout, result.stderr
    except subprocess.TimeoutExpired:
        return False, "", "命令超时"
    except Exception as e:
        return False, "", str(e)


def test_project_structure():
    """测试项目结构"""
    print("🔍 检查项目结构...")
    
    required_dirs = [
        'kafka-consumer-monitor',
        'kafka-data-filter',
        'docs'
    ]
    
    required_files = [
        'kafka-consumer-monitor/README.md',
        'kafka-consumer-monitor/requirements.txt',
        'kafka-consumer-monitor/main.py',
        'kafka-data-filter/README.md',
        'kafka-data-filter/requirements.txt',
        'kafka-data-filter/main.py',
        'docs/DEVELOPMENT_GUIDE.md',
        'docs/PROJECT_OVERVIEW.md'
    ]
    
    all_good = True
    
    for dir_path in required_dirs:
        if os.path.isdir(dir_path):
            print(f"  ✅ 目录存在: {dir_path}")
        else:
            print(f"  ❌ 目录缺失: {dir_path}")
            all_good = False
    
    for file_path in required_files:
        if os.path.isfile(file_path):
            print(f"  ✅ 文件存在: {file_path}")
        else:
            print(f"  ❌ 文件缺失: {file_path}")
            all_good = False
    
    return all_good


def test_project1():
    """测试项目1：Kafka消费者组监控程序"""
    print("\n🔍 测试项目1：Kafka消费者组监控程序...")
    
    # 测试帮助信息
    success, stdout, stderr = run_command("python main.py --help", cwd="kafka-consumer-monitor")
    if success and "Kafka消费者组监控程序" in stdout:
        print("  ✅ 项目1主程序可以正常运行")
    else:
        print("  ❌ 项目1主程序运行失败")
        print(f"     错误: {stderr}")
        return False
    
    # 检查依赖文件
    if os.path.isfile("kafka-consumer-monitor/requirements.txt"):
        print("  ✅ 项目1依赖文件存在")
    else:
        print("  ❌ 项目1依赖文件缺失")
        return False
    
    return True


def test_project2():
    """测试项目2：Kafka数据过滤程序"""
    print("\n🔍 测试项目2：Kafka数据过滤程序...")
    
    # 测试主启动脚本
    success, stdout, stderr = run_command("python main.py --help", cwd="kafka-data-filter")
    if success and "Kafka数据过滤程序" in stdout:
        print("  ✅ 项目2主启动脚本可以正常运行")
    else:
        print("  ❌ 项目2主启动脚本运行失败")
        print(f"     错误: {stderr}")
        return False
    
    # 测试核心模块
    success, stdout, stderr = run_command("python kafka_data_filter/main.py --help", cwd="kafka-data-filter")
    if success and "Kafka数据过滤程序" in stdout:
        print("  ✅ 项目2核心模块可以正常运行")
    else:
        print("  ❌ 项目2核心模块运行失败")
        print(f"     错误: {stderr}")
        return False
    
    # 测试单元测试
    success, stdout, stderr = run_command("python run_tests.py --list", cwd="kafka-data-filter")
    if success and "test_" in stdout:
        print("  ✅ 项目2单元测试框架正常")
    else:
        print("  ❌ 项目2单元测试框架异常")
        print(f"     错误: {stderr}")
        return False
    
    # 检查依赖文件
    if os.path.isfile("kafka-data-filter/requirements.txt"):
        print("  ✅ 项目2依赖文件存在")
    else:
        print("  ❌ 项目2依赖文件缺失")
        return False
    
    return True


def test_independence():
    """测试项目独立性"""
    print("\n🔍 测试项目独立性...")
    
    # 检查模块命名空间
    try:
        import sys
        sys.path.insert(0, 'kafka-consumer-monitor')
        import kafka_consumer_monitor
        print("  ✅ 项目1模块可以独立导入")
    except ImportError as e:
        print(f"  ❌ 项目1模块导入失败: {e}")
        return False
    
    try:
        sys.path.insert(0, 'kafka-data-filter')
        import kafka_data_filter
        print("  ✅ 项目2模块可以独立导入")
    except ImportError as e:
        print(f"  ❌ 项目2模块导入失败: {e}")
        return False
    
    # 检查依赖分离
    with open('kafka-consumer-monitor/requirements.txt', 'r') as f:
        deps1 = set(line.strip().split('==')[0] for line in f if line.strip())
    
    with open('kafka-data-filter/requirements.txt', 'r') as f:
        deps2 = set(line.strip().split('==')[0] for line in f if line.strip())
    
    common_deps = deps1 & deps2
    print(f"  ✅ 共同依赖: {', '.join(common_deps)}")
    print(f"  ✅ 项目1独有依赖: {', '.join(deps1 - deps2)}")
    print(f"  ✅ 项目2独有依赖: {', '.join(deps2 - deps1)}")
    
    return True


def main():
    """主函数"""
    print("=" * 60)
    print("PythonProject 重构验证")
    print("=" * 60)
    
    tests = [
        ("项目结构检查", test_project_structure),
        ("项目1功能测试", test_project1),
        ("项目2功能测试", test_project2),
        ("项目独立性测试", test_independence)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"验证结果: {passed}/{total} 通过")
    print("=" * 60)
    
    if passed == total:
        print("🎉 所有测试通过！项目重构成功！")
        print("\n✅ 两个项目现在完全独立，可以分别使用：")
        print("   cd kafka-consumer-monitor && python main.py --help")
        print("   cd kafka-data-filter && python main.py --help")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关问题。")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
