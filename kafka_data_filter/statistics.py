"""
统计模块

负责收集和管理过滤程序的运行统计信息
"""

import time
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from threading import Lock


class Statistics:
    """统计信息管理类"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化统计模块
        
        参数:
            config: 统计配置字典
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.enabled = config.get('enabled', True)
        self.interval = config.get('interval', 10)  # 统计输出间隔（秒）
        self.show_details = config.get('show_details', True)
        
        # 统计数据
        self.start_time = None
        self.last_report_time = None
        self.total_consumed = 0
        self.total_processed = 0
        self.total_passed = 0
        self.total_output = 0
        self.total_errors = 0
        
        # 性能统计
        self.processing_times = []  # 处理时间列表
        self.throughput_history = []  # 吞吐量历史
        
        # 时间窗口统计
        self.window_size = 60  # 时间窗口大小（秒）
        self.window_data = []  # 时间窗口数据
        
        # 线程安全锁
        self.lock = Lock()
        
        self.logger.info(f"统计模块已初始化，启用状态: {self.enabled}")
    
    def start(self) -> None:
        """开始统计"""
        with self.lock:
            self.start_time = datetime.now()
            self.last_report_time = self.start_time
            self.logger.info("统计已开始")
    
    def record_consumed(self, count: int = 1) -> None:
        """
        记录消费的消息数
        
        参数:
            count: 消费的消息数量
        """
        if not self.enabled:
            return
        
        with self.lock:
            self.total_consumed += count
    
    def record_processed(self, count: int = 1, processing_time: Optional[float] = None) -> None:
        """
        记录处理的消息数
        
        参数:
            count: 处理的消息数量
            processing_time: 处理时间（秒）
        """
        if not self.enabled:
            return
        
        with self.lock:
            self.total_processed += count
            
            # 记录处理时间
            if processing_time is not None:
                self.processing_times.append(processing_time)
                
                # 限制处理时间列表大小
                if len(self.processing_times) > 1000:
                    self.processing_times = self.processing_times[-500:]
            
            # 更新时间窗口数据
            current_time = datetime.now()
            self.window_data.append({
                'timestamp': current_time,
                'processed': count
            })
            
            # 清理过期的窗口数据
            cutoff_time = current_time - timedelta(seconds=self.window_size)
            self.window_data = [
                item for item in self.window_data 
                if item['timestamp'] > cutoff_time
            ]
    
    def record_passed(self, count: int = 1) -> None:
        """
        记录通过过滤的消息数
        
        参数:
            count: 通过过滤的消息数量
        """
        if not self.enabled:
            return
        
        with self.lock:
            self.total_passed += count
    
    def record_output(self, count: int = 1) -> None:
        """
        记录输出的消息数
        
        参数:
            count: 输出的消息数量
        """
        if not self.enabled:
            return
        
        with self.lock:
            self.total_output += count
    
    def record_error(self, count: int = 1) -> None:
        """
        记录错误数
        
        参数:
            count: 错误数量
        """
        if not self.enabled:
            return
        
        with self.lock:
            self.total_errors += count
    
    def should_report(self) -> bool:
        """
        检查是否应该输出统计报告
        
        返回:
            是否应该输出报告
        """
        if not self.enabled or not self.last_report_time:
            return False
        
        current_time = datetime.now()
        elapsed = (current_time - self.last_report_time).total_seconds()
        
        return elapsed >= self.interval
    
    def get_current_statistics(self) -> Dict[str, Any]:
        """
        获取当前统计信息
        
        返回:
            统计信息字典
        """
        with self.lock:
            current_time = datetime.now()
            
            # 计算运行时间
            if self.start_time:
                runtime = (current_time - self.start_time).total_seconds()
            else:
                runtime = 0
            
            # 计算通过率
            pass_rate = (self.total_passed / self.total_processed * 100) if self.total_processed > 0 else 0
            
            # 计算平均处理时间
            avg_processing_time = (
                sum(self.processing_times) / len(self.processing_times) 
                if self.processing_times else 0
            )
            
            # 计算当前吞吐量（每秒处理消息数）
            current_throughput = self._calculate_current_throughput()
            
            # 计算总体吞吐量
            overall_throughput = self.total_processed / runtime if runtime > 0 else 0
            
            statistics = {
                'runtime_seconds': round(runtime, 2),
                'total_consumed': self.total_consumed,
                'total_processed': self.total_processed,
                'total_passed': self.total_passed,
                'total_filtered': self.total_processed - self.total_passed,
                'total_output': self.total_output,
                'total_errors': self.total_errors,
                'pass_rate': round(pass_rate, 2),
                'avg_processing_time_ms': round(avg_processing_time * 1000, 2),
                'current_throughput': round(current_throughput, 2),
                'overall_throughput': round(overall_throughput, 2),
                'timestamp': current_time.isoformat()
            }
            
            # 添加详细信息
            if self.show_details:
                statistics.update({
                    'processing_time_samples': len(self.processing_times),
                    'window_size_seconds': self.window_size,
                    'window_data_points': len(self.window_data)
                })
            
            return statistics
    
    def _calculate_current_throughput(self) -> float:
        """
        计算当前时间窗口内的吞吐量
        
        返回:
            当前吞吐量（消息/秒）
        """
        if not self.window_data:
            return 0.0
        
        # 计算时间窗口内的总处理数
        total_in_window = sum(item['processed'] for item in self.window_data)
        
        # 计算时间窗口的实际长度
        if len(self.window_data) < 2:
            return 0.0
        
        oldest_time = min(item['timestamp'] for item in self.window_data)
        newest_time = max(item['timestamp'] for item in self.window_data)
        window_duration = (newest_time - oldest_time).total_seconds()
        
        if window_duration <= 0:
            return 0.0
        
        return total_in_window / window_duration
    
    def generate_report(self) -> str:
        """
        生成统计报告
        
        返回:
            格式化的统计报告字符串
        """
        stats = self.get_current_statistics()
        
        lines = []
        lines.append(f"运行时间: {stats['runtime_seconds']} 秒")
        lines.append(f"消费消息数: {stats['total_consumed']}")
        lines.append(f"处理消息数: {stats['total_processed']}")
        lines.append(f"通过过滤数: {stats['total_passed']}")
        lines.append(f"被过滤数: {stats['total_filtered']}")
        lines.append(f"输出消息数: {stats['total_output']}")
        lines.append(f"错误数: {stats['total_errors']}")
        lines.append(f"通过率: {stats['pass_rate']}%")
        lines.append(f"平均处理时间: {stats['avg_processing_time_ms']} ms")
        lines.append(f"当前吞吐量: {stats['current_throughput']} 消息/秒")
        lines.append(f"总体吞吐量: {stats['overall_throughput']} 消息/秒")
        
        return '\n'.join(lines)
    
    def print_report(self) -> None:
        """打印统计报告到控制台"""
        if not self.enabled:
            return
        
        report = self.generate_report()
        
        print("\n" + "=" * 50)
        print("实时统计信息")
        print("=" * 50)
        print(report)
        print("=" * 50)
        
        # 更新最后报告时间
        with self.lock:
            self.last_report_time = datetime.now()
    
    def reset(self) -> None:
        """重置所有统计数据"""
        with self.lock:
            self.start_time = None
            self.last_report_time = None
            self.total_consumed = 0
            self.total_processed = 0
            self.total_passed = 0
            self.total_output = 0
            self.total_errors = 0
            self.processing_times.clear()
            self.throughput_history.clear()
            self.window_data.clear()
            
        self.logger.info("统计数据已重置")
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """
        获取性能指标
        
        返回:
            性能指标字典
        """
        with self.lock:
            if not self.processing_times:
                return {
                    'min_processing_time_ms': 0,
                    'max_processing_time_ms': 0,
                    'avg_processing_time_ms': 0,
                    'median_processing_time_ms': 0
                }
            
            sorted_times = sorted(self.processing_times)
            n = len(sorted_times)
            
            return {
                'min_processing_time_ms': round(min(sorted_times) * 1000, 2),
                'max_processing_time_ms': round(max(sorted_times) * 1000, 2),
                'avg_processing_time_ms': round(sum(sorted_times) / n * 1000, 2),
                'median_processing_time_ms': round(sorted_times[n // 2] * 1000, 2),
                'p95_processing_time_ms': round(sorted_times[int(n * 0.95)] * 1000, 2),
                'p99_processing_time_ms': round(sorted_times[int(n * 0.99)] * 1000, 2)
            }
