"""
Kafka数据过滤程序

这是一个功能完整的Kafka数据过滤程序，支持：
- 从Kafka topic消费JSON格式消息
- 复杂的过滤规则（逻辑运算符、比较运算符、括号分组）
- 多层嵌套字段访问
- 统计功能和多种输出方式

作者: AI Assistant
版本: 1.0.0
"""

__version__ = "1.0.0"
__author__ = "AI Assistant"

from .config_manager import ConfigManager
from .data_accessor import DataAccessor
from .filter_parser import FilterParser
from .kafka_consumer import KafkaConsumer
from .data_filter import DataFilter
from .output_handler import OutputHandler
from .statistics import Statistics

__all__ = [
    'ConfigManager',
    'DataAccessor', 
    'FilterParser',
    'KafkaConsumer',
    'DataFilter',
    'OutputHandler',
    'Statistics'
]
