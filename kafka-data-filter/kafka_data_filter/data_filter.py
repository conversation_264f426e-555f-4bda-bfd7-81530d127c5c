"""
数据过滤器模块

负责应用过滤规则到消息数据，支持多个过滤规则组合
"""

import logging
from typing import List, Dict, Any, Optional, Tuple
from .filter_parser import FilterParser


class DataFilter:
    """数据过滤器类，负责执行数据过滤逻辑"""
    
    def __init__(self, filter_rules: List[str]):
        """
        初始化数据过滤器
        
        参数:
            filter_rules: 过滤规则字符串列表
        """
        self.filter_rules = filter_rules
        self.parser = FilterParser()
        self.logger = logging.getLogger(__name__)
        self.parsed_rules = []
        self.total_processed = 0
        self.total_passed = 0
        self.rule_statistics = {}
        
        # 预解析所有规则
        self._parse_rules()
    
    def _parse_rules(self) -> None:
        """预解析所有过滤规则"""
        self.parsed_rules = []
        
        for i, rule in enumerate(self.filter_rules):
            try:
                parsed_rule = self.parser.parse(rule)
                self.parsed_rules.append({
                    'index': i,
                    'rule': rule,
                    'parsed': parsed_rule
                })
                
                # 初始化规则统计
                self.rule_statistics[i] = {
                    'rule': rule,
                    'processed': 0,
                    'passed': 0,
                    'failed': 0
                }
                
                self.logger.debug(f"成功解析规则 {i}: {rule}")
                
            except Exception as e:
                self.logger.error(f"解析规则 {i} 失败: {rule}, 错误: {e}")
                raise ValueError(f"过滤规则解析失败: {e}")
        
        self.logger.info(f"成功解析 {len(self.parsed_rules)} 个过滤规则")
    
    def filter_message(self, data: dict) -> Tuple[bool, Dict[str, Any]]:
        """
        对单条消息执行过滤
        
        参数:
            data: 要过滤的消息数据
            
        返回:
            (是否通过过滤, 过滤详情)
        """
        self.total_processed += 1
        
        filter_results = {
            'passed': False,
            'rule_results': [],
            'data': data
        }
        
        # 如果没有规则，默认通过
        if not self.parsed_rules:
            filter_results['passed'] = True
            self.total_passed += 1
            return True, filter_results
        
        # 执行所有规则（OR逻辑：任一规则通过即可）
        overall_passed = False
        
        for rule_info in self.parsed_rules:
            rule_index = rule_info['index']
            rule_text = rule_info['rule']
            parsed_rule = rule_info['parsed']
            
            try:
                # 执行规则
                rule_passed = self.parser.evaluate(parsed_rule, data)
                
                # 更新规则统计
                self.rule_statistics[rule_index]['processed'] += 1
                if rule_passed:
                    self.rule_statistics[rule_index]['passed'] += 1
                    overall_passed = True
                else:
                    self.rule_statistics[rule_index]['failed'] += 1
                
                # 记录规则结果
                filter_results['rule_results'].append({
                    'index': rule_index,
                    'rule': rule_text,
                    'passed': rule_passed
                })
                
                self.logger.debug(f"规则 {rule_index} 结果: {rule_passed}")
                
            except Exception as e:
                self.logger.error(f"执行规则 {rule_index} 失败: {e}")
                
                # 规则执行失败视为不通过
                self.rule_statistics[rule_index]['processed'] += 1
                self.rule_statistics[rule_index]['failed'] += 1
                
                filter_results['rule_results'].append({
                    'index': rule_index,
                    'rule': rule_text,
                    'passed': False,
                    'error': str(e)
                })
        
        filter_results['passed'] = overall_passed
        
        if overall_passed:
            self.total_passed += 1
        
        return overall_passed, filter_results
    
    def filter_messages_batch(self, messages: List[dict]) -> Tuple[List[dict], List[Dict[str, Any]]]:
        """
        批量过滤消息
        
        参数:
            messages: 消息列表
            
        返回:
            (通过过滤的消息列表, 过滤详情列表)
        """
        passed_messages = []
        filter_details = []
        
        for message in messages:
            passed, details = self.filter_message(message)
            
            if passed:
                passed_messages.append(message)
            
            filter_details.append(details)
        
        return passed_messages, filter_details
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取过滤统计信息
        
        返回:
            统计信息字典
        """
        pass_rate = (self.total_passed / self.total_processed * 100) if self.total_processed > 0 else 0
        
        return {
            'total_processed': self.total_processed,
            'total_passed': self.total_passed,
            'total_filtered': self.total_processed - self.total_passed,
            'pass_rate': round(pass_rate, 2),
            'rule_count': len(self.parsed_rules),
            'rule_statistics': self.rule_statistics.copy()
        }
    
    def reset_statistics(self) -> None:
        """重置统计信息"""
        self.total_processed = 0
        self.total_passed = 0
        
        for rule_stat in self.rule_statistics.values():
            rule_stat['processed'] = 0
            rule_stat['passed'] = 0
            rule_stat['failed'] = 0
        
        self.logger.info("过滤统计信息已重置")
    
    def add_rule(self, rule: str) -> None:
        """
        添加新的过滤规则
        
        参数:
            rule: 过滤规则字符串
        """
        try:
            # 解析新规则
            parsed_rule = self.parser.parse(rule)
            
            # 添加到规则列表
            rule_index = len(self.parsed_rules)
            self.filter_rules.append(rule)
            self.parsed_rules.append({
                'index': rule_index,
                'rule': rule,
                'parsed': parsed_rule
            })
            
            # 初始化统计
            self.rule_statistics[rule_index] = {
                'rule': rule,
                'processed': 0,
                'passed': 0,
                'failed': 0
            }
            
            self.logger.info(f"成功添加过滤规则: {rule}")
            
        except Exception as e:
            self.logger.error(f"添加过滤规则失败: {rule}, 错误: {e}")
            raise ValueError(f"添加过滤规则失败: {e}")
    
    def remove_rule(self, index: int) -> None:
        """
        移除指定索引的过滤规则
        
        参数:
            index: 规则索引
        """
        if 0 <= index < len(self.parsed_rules):
            removed_rule = self.parsed_rules.pop(index)
            self.filter_rules.pop(index)
            
            # 移除统计信息
            if index in self.rule_statistics:
                del self.rule_statistics[index]
            
            # 重新索引剩余规则
            for i, rule_info in enumerate(self.parsed_rules):
                rule_info['index'] = i
            
            self.logger.info(f"成功移除过滤规则: {removed_rule['rule']}")
        else:
            raise ValueError(f"规则索引超出范围: {index}")
    
    def get_rules(self) -> List[Dict[str, Any]]:
        """
        获取所有过滤规则信息
        
        返回:
            规则信息列表
        """
        return [
            {
                'index': rule_info['index'],
                'rule': rule_info['rule'],
                'statistics': self.rule_statistics.get(rule_info['index'], {})
            }
            for rule_info in self.parsed_rules
        ]
    
    def validate_rule(self, rule: str) -> Tuple[bool, Optional[str]]:
        """
        验证过滤规则语法
        
        参数:
            rule: 过滤规则字符串
            
        返回:
            (是否有效, 错误信息)
        """
        try:
            self.parser.parse(rule)
            return True, None
        except Exception as e:
            return False, str(e)
