"""
Kafka消费者模块

负责连接Kafka集群，消费指定topic的消息，并解析JSON格式的消息数据
"""

import json
import logging
from typing import Dict, Any, Iterator, Optional, Callable
from kafka import KafkaConsumer
from kafka.errors import KafkaError, KafkaTimeoutError
import time


class KafkaMessageConsumer:
    """Kafka消息消费者类"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化Kafka消费者
        
        参数:
            config: Kafka消费者配置字典
        """
        self.config = config
        self.consumer = None
        self.topic = None
        self.logger = logging.getLogger(__name__)
        self.is_running = False
        self.message_count = 0
        self.error_count = 0
        
    def connect(self, topic: str) -> None:
        """
        连接到Kafka集群并订阅指定topic
        
        参数:
            topic: 要订阅的topic名称
        """
        try:
            self.topic = topic
            
            # 创建Kafka消费者
            self.consumer = KafkaConsumer(
                topic,
                **self.config
            )
            
            self.logger.info(f"成功连接到Kafka集群，订阅topic: {topic}")
            self.logger.info(f"消费者配置: {self._get_safe_config()}")
            
        except Exception as e:
            self.logger.error(f"连接Kafka失败: {e}")
            raise RuntimeError(f"Kafka连接失败: {e}")
    
    def _get_safe_config(self) -> Dict[str, Any]:
        """
        获取安全的配置信息（隐藏敏感信息）
        
        返回:
            安全的配置字典
        """
        safe_config = self.config.copy()
        
        # 隐藏敏感信息
        sensitive_keys = ['sasl_password', 'ssl_password']
        for key in sensitive_keys:
            if key in safe_config:
                safe_config[key] = '***'
        
        return safe_config
    
    def consume_messages(self, 
                        message_handler: Callable[[dict], None],
                        max_messages: Optional[int] = None,
                        timeout_ms: int = 1000) -> None:
        """
        消费消息并调用处理函数
        
        参数:
            message_handler: 消息处理函数，接收解析后的JSON数据
            max_messages: 最大消费消息数量，None表示无限制
            timeout_ms: 消费超时时间（毫秒）
        """
        if not self.consumer:
            raise RuntimeError("消费者未连接，请先调用connect方法")
        
        self.is_running = True
        self.message_count = 0
        self.error_count = 0
        
        self.logger.info(f"开始消费消息，最大消息数: {max_messages or '无限制'}")
        
        try:
            while self.is_running:
                # 检查是否达到最大消息数
                if max_messages and self.message_count >= max_messages:
                    self.logger.info(f"已达到最大消息数限制: {max_messages}")
                    break
                
                # 拉取消息
                message_batch = self.consumer.poll(timeout_ms=timeout_ms)
                
                if not message_batch:
                    continue
                
                # 处理消息批次
                for topic_partition, messages in message_batch.items():
                    for message in messages:
                        try:
                            # 解析JSON消息
                            json_data = self._parse_message(message)
                            
                            if json_data is not None:
                                # 调用消息处理函数
                                message_handler(json_data)
                                self.message_count += 1
                                
                                # 记录处理进度
                                if self.message_count % 100 == 0:
                                    self.logger.info(f"已处理消息数: {self.message_count}")
                            
                        except Exception as e:
                            self.error_count += 1
                            self.logger.error(f"处理消息失败: {e}")
                            
                            # 如果错误太多，停止消费
                            if self.error_count > 100:
                                self.logger.error("错误数量过多，停止消费")
                                self.stop()
                                break
                
        except KeyboardInterrupt:
            self.logger.info("收到中断信号，停止消费")
        except KafkaTimeoutError:
            self.logger.warning("Kafka消费超时")
        except KafkaError as e:
            self.logger.error(f"Kafka错误: {e}")
            raise
        except Exception as e:
            self.logger.error(f"消费消息时发生未知错误: {e}")
            raise
        finally:
            self.is_running = False
            self.logger.info(f"消费结束，总处理消息数: {self.message_count}, 错误数: {self.error_count}")
    
    def _parse_message(self, message) -> Optional[dict]:
        """
        解析Kafka消息为JSON数据
        
        参数:
            message: Kafka消息对象
            
        返回:
            解析后的JSON数据，解析失败返回None
        """
        try:
            # 获取消息值
            message_value = message.value
            
            if message_value is None:
                self.logger.debug("收到空消息")
                return None
            
            # 如果是字节类型，先解码
            if isinstance(message_value, bytes):
                message_value = message_value.decode('utf-8')
            
            # 解析JSON
            json_data = json.loads(message_value)
            
            # 添加消息元数据
            json_data['_kafka_metadata'] = {
                'topic': message.topic,
                'partition': message.partition,
                'offset': message.offset,
                'timestamp': message.timestamp,
                'key': message.key.decode('utf-8') if message.key else None
            }
            
            return json_data
            
        except json.JSONDecodeError as e:
            self.logger.warning(f"JSON解析失败: {e}, 消息内容: {message_value}")
            return None
        except UnicodeDecodeError as e:
            self.logger.warning(f"消息解码失败: {e}")
            return None
        except Exception as e:
            self.logger.error(f"解析消息时发生未知错误: {e}")
            return None
    
    def stop(self) -> None:
        """停止消费消息"""
        self.is_running = False
        self.logger.info("停止消费消息")
    
    def close(self) -> None:
        """关闭消费者连接"""
        if self.consumer:
            try:
                self.consumer.close()
                self.logger.info("Kafka消费者连接已关闭")
            except Exception as e:
                self.logger.error(f"关闭Kafka消费者失败: {e}")
            finally:
                self.consumer = None
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取消费统计信息
        
        返回:
            统计信息字典
        """
        return {
            'message_count': self.message_count,
            'error_count': self.error_count,
            'is_running': self.is_running,
            'topic': self.topic
        }
    
    def seek_to_beginning(self) -> None:
        """将消费者位置重置到topic开始位置"""
        if self.consumer:
            self.consumer.seek_to_beginning()
            self.logger.info("消费者位置已重置到topic开始位置")
    
    def seek_to_end(self) -> None:
        """将消费者位置设置到topic结束位置"""
        if self.consumer:
            self.consumer.seek_to_end()
            self.logger.info("消费者位置已设置到topic结束位置")
    
    def __enter__(self):
        """上下文管理器入口"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.close()
