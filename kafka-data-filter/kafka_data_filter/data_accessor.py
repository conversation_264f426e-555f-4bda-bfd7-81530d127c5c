"""
数据访问器模块

负责安全地访问JSON数据中的多层嵌套字段
支持点号分隔的字段路径，如：字段2.class2
"""

from typing import Any, Optional, Union, List
import logging


class DataAccessor:
    """数据访问器类，提供安全的嵌套字段访问功能"""
    
    def __init__(self):
        """初始化数据访问器"""
        self.logger = logging.getLogger(__name__)
    
    def get_field_value(self, data: dict, field_path: str) -> Any:
        """
        从数据中获取指定字段路径的值
        
        参数:
            data: 要访问的数据字典
            field_path: 字段路径，支持点号分隔的嵌套访问，如 "字段2.class2"
            
        返回:
            字段值，如果字段不存在则返回None
        """
        if not isinstance(data, dict):
            self.logger.warning(f"数据不是字典类型: {type(data)}")
            return None
        
        if not field_path:
            self.logger.warning("字段路径为空")
            return None
        
        try:
            # 分割字段路径
            path_parts = field_path.split('.')
            current_value = data
            
            for part in path_parts:
                if not part:  # 跳过空字符串
                    continue
                    
                if isinstance(current_value, dict):
                    current_value = current_value.get(part)
                elif isinstance(current_value, list):
                    # 如果是列表，尝试将part转换为索引
                    try:
                        index = int(part)
                        if 0 <= index < len(current_value):
                            current_value = current_value[index]
                        else:
                            self.logger.debug(f"列表索引超出范围: {index}, 列表长度: {len(current_value)}")
                            return None
                    except ValueError:
                        self.logger.debug(f"无法将'{part}'转换为列表索引")
                        return None
                else:
                    self.logger.debug(f"字段路径'{field_path}'在'{part}'处中断，当前值类型: {type(current_value)}")
                    return None
                
                # 如果中间某个值为None，直接返回None
                if current_value is None:
                    return None
            
            return current_value
            
        except Exception as e:
            self.logger.error(f"访问字段'{field_path}'时发生错误: {e}")
            return None
    
    def field_exists(self, data: dict, field_path: str) -> bool:
        """
        检查指定字段路径是否存在
        
        参数:
            data: 要检查的数据字典
            field_path: 字段路径
            
        返回:
            字段是否存在
        """
        if not isinstance(data, dict) or not field_path:
            return False
        
        try:
            path_parts = field_path.split('.')
            current_value = data
            
            for part in path_parts:
                if not part:
                    continue
                    
                if isinstance(current_value, dict):
                    if part not in current_value:
                        return False
                    current_value = current_value[part]
                elif isinstance(current_value, list):
                    try:
                        index = int(part)
                        if not (0 <= index < len(current_value)):
                            return False
                        current_value = current_value[index]
                    except ValueError:
                        return False
                else:
                    return False
            
            return True
            
        except Exception:
            return False
    
    def get_field_type(self, data: dict, field_path: str) -> Optional[type]:
        """
        获取指定字段的数据类型
        
        参数:
            data: 数据字典
            field_path: 字段路径
            
        返回:
            字段的数据类型，如果字段不存在则返回None
        """
        value = self.get_field_value(data, field_path)
        return type(value) if value is not None else None
    
    def is_field_numeric(self, data: dict, field_path: str) -> bool:
        """
        检查字段是否为数值类型
        
        参数:
            data: 数据字典
            field_path: 字段路径
            
        返回:
            字段是否为数值类型
        """
        value = self.get_field_value(data, field_path)
        return isinstance(value, (int, float))
    
    def is_field_string(self, data: dict, field_path: str) -> bool:
        """
        检查字段是否为字符串类型
        
        参数:
            data: 数据字典
            field_path: 字段路径
            
        返回:
            字段是否为字符串类型
        """
        value = self.get_field_value(data, field_path)
        return isinstance(value, str)
    
    def is_field_list(self, data: dict, field_path: str) -> bool:
        """
        检查字段是否为列表类型
        
        参数:
            data: 数据字典
            field_path: 字段路径
            
        返回:
            字段是否为列表类型
        """
        value = self.get_field_value(data, field_path)
        return isinstance(value, list)
    
    def convert_to_comparable(self, value: Any) -> Any:
        """
        将值转换为可比较的类型
        
        参数:
            value: 要转换的值
            
        返回:
            转换后的值
        """
        if value is None:
            return None
        
        # 如果是字符串，尝试转换为数字
        if isinstance(value, str):
            # 尝试转换为整数
            try:
                return int(value)
            except ValueError:
                pass
            
            # 尝试转换为浮点数
            try:
                return float(value)
            except ValueError:
                pass
            
            # 保持字符串
            return value
        
        # 其他类型直接返回
        return value
