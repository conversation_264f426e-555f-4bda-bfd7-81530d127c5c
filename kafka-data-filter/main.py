#!/usr/bin/env python3
"""
Kafka数据过滤程序启动脚本

提供命令行接口来运行Kafka数据过滤程序
"""

import sys
import os
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from kafka_data_filter.main import KafkaDataFilterApp


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="Kafka数据过滤程序",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python main.py filter_config.yaml
  python main.py filter_config.yaml --max-messages 1000
  python main.py example_config.yaml --max-messages 500 --verbose
        """
    )
    
    parser.add_argument(
        "config",
        help="配置文件路径"
    )
    
    parser.add_argument(
        "--max-messages",
        type=int,
        help="最大处理消息数量（默认无限制）"
    )
    
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="启用详细输出"
    )
    
    parser.add_argument(
        "--version",
        action="version",
        version="Kafka数据过滤程序 v1.0.0"
    )
    
    args = parser.parse_args()
    
    # 检查配置文件是否存在
    if not os.path.exists(args.config):
        print(f"错误: 配置文件不存在: {args.config}")
        sys.exit(1)
    
    try:
        # 创建并运行应用程序
        app = KafkaDataFilterApp(args.config)
        
        print("=" * 60)
        print("Kafka数据过滤程序")
        print("=" * 60)
        print(f"配置文件: {args.config}")
        print(f"最大消息数: {args.max_messages or '无限制'}")
        print(f"详细模式: {'启用' if args.verbose else '禁用'}")
        print("=" * 60)
        print("按 Ctrl+C 停止程序")
        print("=" * 60)
        
        # 运行程序
        app.run(max_messages=args.max_messages)
        
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序运行失败: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
