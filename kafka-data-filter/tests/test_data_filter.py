"""
数据过滤器单元测试
"""

import unittest
from kafka_data_filter.data_filter import DataFilter


class TestDataFilter(unittest.TestCase):
    """数据过滤器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.filter_rules = [
            'field1 = 1 AND field2.class2 NOT IN ("广告信息", "股票资讯")',
            'score > 80 AND status = "active"'
        ]
        self.data_filter = DataFilter(self.filter_rules)
        
        self.test_data_1 = {
            'field1': 1,
            'field2': {
                'class2': '新闻资讯'
            },
            'score': 85,
            'status': 'active'
        }
        
        self.test_data_2 = {
            'field1': 2,
            'field2': {
                'class2': '广告信息'
            },
            'score': 75,
            'status': 'inactive'
        }
        
        self.test_data_3 = {
            'field1': 1,
            'field2': {
                'class2': '技术资讯'
            },
            'score': 90,
            'status': 'active'
        }
    
    def test_init_with_rules(self):
        """测试使用规则初始化"""
        self.assertEqual(len(self.data_filter.parsed_rules), 2)
        self.assertEqual(len(self.data_filter.rule_statistics), 2)
    
    def test_filter_message_pass_first_rule(self):
        """测试消息通过第一个规则"""
        passed, details = self.data_filter.filter_message(self.test_data_1)
        
        self.assertTrue(passed)
        self.assertTrue(details['passed'])
        self.assertEqual(len(details['rule_results']), 2)
        
        # 第一个规则应该通过
        self.assertTrue(details['rule_results'][0]['passed'])
    
    def test_filter_message_pass_second_rule(self):
        """测试消息通过第二个规则"""
        passed, details = self.data_filter.filter_message(self.test_data_3)
        
        self.assertTrue(passed)
        self.assertTrue(details['passed'])
        
        # 第二个规则应该通过
        self.assertTrue(details['rule_results'][1]['passed'])
    
    def test_filter_message_fail_all_rules(self):
        """测试消息不通过任何规则"""
        passed, details = self.data_filter.filter_message(self.test_data_2)
        
        self.assertFalse(passed)
        self.assertFalse(details['passed'])
        
        # 所有规则都应该不通过
        for rule_result in details['rule_results']:
            self.assertFalse(rule_result['passed'])
    
    def test_filter_messages_batch(self):
        """测试批量过滤消息"""
        messages = [self.test_data_1, self.test_data_2, self.test_data_3]
        passed_messages, filter_details = self.data_filter.filter_messages_batch(messages)
        
        # 应该有2条消息通过（test_data_1和test_data_3）
        self.assertEqual(len(passed_messages), 2)
        self.assertEqual(len(filter_details), 3)
        
        # 验证通过的消息
        self.assertIn(self.test_data_1, passed_messages)
        self.assertIn(self.test_data_3, passed_messages)
        self.assertNotIn(self.test_data_2, passed_messages)
    
    def test_get_statistics(self):
        """测试获取统计信息"""
        # 处理一些消息
        self.data_filter.filter_message(self.test_data_1)
        self.data_filter.filter_message(self.test_data_2)
        self.data_filter.filter_message(self.test_data_3)
        
        stats = self.data_filter.get_statistics()
        
        self.assertEqual(stats['total_processed'], 3)
        self.assertEqual(stats['total_passed'], 2)
        self.assertEqual(stats['total_filtered'], 1)
        self.assertEqual(stats['rule_count'], 2)
        self.assertAlmostEqual(stats['pass_rate'], 66.67, places=1)
    
    def test_reset_statistics(self):
        """测试重置统计信息"""
        # 处理一些消息
        self.data_filter.filter_message(self.test_data_1)
        self.data_filter.filter_message(self.test_data_2)
        
        # 重置统计
        self.data_filter.reset_statistics()
        
        stats = self.data_filter.get_statistics()
        self.assertEqual(stats['total_processed'], 0)
        self.assertEqual(stats['total_passed'], 0)
    
    def test_add_rule(self):
        """测试添加新规则"""
        new_rule = 'field3 = "test"'
        initial_count = len(self.data_filter.parsed_rules)
        
        self.data_filter.add_rule(new_rule)
        
        self.assertEqual(len(self.data_filter.parsed_rules), initial_count + 1)
        self.assertIn(new_rule, self.data_filter.filter_rules)
    
    def test_add_invalid_rule(self):
        """测试添加无效规则"""
        invalid_rule = 'invalid rule syntax'
        
        with self.assertRaises(ValueError):
            self.data_filter.add_rule(invalid_rule)
    
    def test_remove_rule(self):
        """测试移除规则"""
        initial_count = len(self.data_filter.parsed_rules)
        
        self.data_filter.remove_rule(0)
        
        self.assertEqual(len(self.data_filter.parsed_rules), initial_count - 1)
    
    def test_remove_rule_invalid_index(self):
        """测试移除无效索引的规则"""
        with self.assertRaises(ValueError):
            self.data_filter.remove_rule(999)
    
    def test_get_rules(self):
        """测试获取规则信息"""
        rules_info = self.data_filter.get_rules()
        
        self.assertEqual(len(rules_info), 2)
        
        for rule_info in rules_info:
            self.assertIn('index', rule_info)
            self.assertIn('rule', rule_info)
            self.assertIn('statistics', rule_info)
    
    def test_validate_rule_valid(self):
        """测试验证有效规则"""
        valid_rule = 'field1 = 1 AND field2 > 10'
        is_valid, error = self.data_filter.validate_rule(valid_rule)
        
        self.assertTrue(is_valid)
        self.assertIsNone(error)
    
    def test_validate_rule_invalid(self):
        """测试验证无效规则"""
        invalid_rule = 'invalid syntax'
        is_valid, error = self.data_filter.validate_rule(invalid_rule)
        
        self.assertFalse(is_valid)
        self.assertIsNotNone(error)
    
    def test_empty_rules(self):
        """测试空规则列表"""
        empty_filter = DataFilter([])
        
        passed, details = empty_filter.filter_message(self.test_data_1)
        
        # 没有规则时应该默认通过
        self.assertTrue(passed)
        self.assertTrue(details['passed'])
    
    def test_rule_statistics_tracking(self):
        """测试规则统计跟踪"""
        # 处理消息，第一个规则通过，第二个规则不通过
        self.data_filter.filter_message(self.test_data_1)
        
        stats = self.data_filter.get_statistics()
        rule_stats = stats['rule_statistics']
        
        # 第一个规则统计
        rule_0_stats = rule_stats[0]
        self.assertEqual(rule_0_stats['processed'], 1)
        self.assertEqual(rule_0_stats['passed'], 1)
        self.assertEqual(rule_0_stats['failed'], 0)
        
        # 第二个规则统计
        rule_1_stats = rule_stats[1]
        self.assertEqual(rule_1_stats['processed'], 1)
        self.assertEqual(rule_1_stats['passed'], 1)  # 第二个规则也通过了
        self.assertEqual(rule_1_stats['failed'], 0)
    
    def test_filter_with_missing_fields(self):
        """测试过滤包含缺失字段的数据"""
        incomplete_data = {
            'field1': 1
            # 缺少field2和其他字段
        }
        
        passed, details = self.data_filter.filter_message(incomplete_data)
        
        # 由于字段缺失，规则应该不通过
        self.assertFalse(passed)
    
    def test_filter_with_null_values(self):
        """测试过滤包含null值的数据"""
        null_data = {
            'field1': None,
            'field2': {
                'class2': None
            },
            'score': None,
            'status': None
        }
        
        passed, details = self.data_filter.filter_message(null_data)
        
        # null值应该导致比较失败
        self.assertFalse(passed)


if __name__ == '__main__':
    unittest.main()
