"""
集成测试

测试各个模块之间的集成功能
"""

import unittest
import tempfile
import os
import yaml
import json
from unittest.mock import Mock, patch
from kafka_data_filter.config_manager import ConfigManager
from kafka_data_filter.data_filter import DataFilter
from kafka_data_filter.output_handler import Output<PERSON>and<PERSON>
from kafka_data_filter.statistics import Statistics


class TestIntegration(unittest.TestCase):
    """集成测试类"""
    
    def setUp(self):
        """测试前准备"""
        # 创建测试配置
        self.test_config = {
            'kafka': {
                'bootstrap_servers': 'localhost:9092',
                'topic': 'test-topic',
                'group_id': 'test-group'
            },
            'filter': {
                'rules': [
                    'field1 = 1 AND field2.class2 NOT IN ("广告信息", "股票资讯")',
                    'score > 80 AND status = "active"'
                ]
            },
            'output': {
                'type': 'console',
                'format': 'pretty_json',
                'show_filter_details': True
            },
            'statistics': {
                'enabled': True,
                'interval': 5,
                'show_details': True
            },
            'logging': {
                'level': 'INFO'
            }
        }
        
        # 创建临时配置文件
        self.temp_config_file = tempfile.NamedTemporaryFile(
            mode='w', suffix='.yaml', delete=False
        )
        yaml.dump(self.test_config, self.temp_config_file, 
                 default_flow_style=False, allow_unicode=True)
        self.temp_config_file.close()
        
        # 测试数据
        self.test_messages = [
            {
                'field1': 1,
                'field2': {'class2': '新闻资讯'},
                'score': 85,
                'status': 'active',
                'title': '重要新闻'
            },
            {
                'field1': 2,
                'field2': {'class2': '广告信息'},
                'score': 60,
                'status': 'inactive',
                'title': '广告内容'
            },
            {
                'field1': 3,
                'field2': {'class2': '技术资讯'},
                'score': 90,
                'status': 'active',
                'title': '技术更新'
            }
        ]
    
    def tearDown(self):
        """测试后清理"""
        if os.path.exists(self.temp_config_file.name):
            os.unlink(self.temp_config_file.name)
    
    def test_config_to_filter_integration(self):
        """测试配置管理器与数据过滤器集成"""
        # 加载配置
        config_manager = ConfigManager(self.temp_config_file.name)
        filter_rules = config_manager.get_filter_rules()
        
        # 创建数据过滤器
        data_filter = DataFilter(filter_rules)
        
        # 验证规则加载
        self.assertEqual(len(data_filter.parsed_rules), 2)
        
        # 测试过滤功能
        passed, details = data_filter.filter_message(self.test_messages[0])
        self.assertTrue(passed)
        
        passed, details = data_filter.filter_message(self.test_messages[1])
        self.assertFalse(passed)
    
    def test_filter_to_output_integration(self):
        """测试数据过滤器与输出处理器集成"""
        # 创建组件
        config_manager = ConfigManager(self.temp_config_file.name)
        data_filter = DataFilter(config_manager.get_filter_rules())
        output_handler = OutputHandler(config_manager.get_output_config())
        
        # 过滤消息
        passed_messages = []
        filter_details_list = []
        
        for message in self.test_messages:
            passed, details = data_filter.filter_message(message)
            if passed:
                passed_messages.append(message)
                filter_details_list.append(details)
        
        # 验证过滤结果
        self.assertEqual(len(passed_messages), 2)  # 第1和第3条消息应该通过
        
        # 测试输出（使用mock避免实际输出）
        with patch('builtins.print') as mock_print:
            output_handler.output_messages_batch(passed_messages, filter_details_list)
            
            # 验证输出被调用
            self.assertEqual(mock_print.call_count, len(passed_messages))
    
    def test_filter_with_statistics_integration(self):
        """测试数据过滤器与统计模块集成"""
        # 创建组件
        config_manager = ConfigManager(self.temp_config_file.name)
        data_filter = DataFilter(config_manager.get_filter_rules())
        statistics = Statistics(config_manager.get_statistics_config())
        
        # 启动统计
        statistics.start()
        
        # 处理消息并更新统计
        for message in self.test_messages:
            statistics.record_consumed()
            
            passed, details = data_filter.filter_message(message)
            statistics.record_processed()
            
            if passed:
                statistics.record_passed()
                statistics.record_output()
        
        # 验证统计信息
        stats = statistics.get_current_statistics()
        self.assertEqual(stats['total_consumed'], 3)
        self.assertEqual(stats['total_processed'], 3)
        self.assertEqual(stats['total_passed'], 2)
        self.assertEqual(stats['total_output'], 2)
        
        # 验证过滤器统计
        filter_stats = data_filter.get_statistics()
        self.assertEqual(filter_stats['total_processed'], 3)
        self.assertEqual(filter_stats['total_passed'], 2)
        self.assertAlmostEqual(filter_stats['pass_rate'], 66.67, places=1)
    
    def test_complete_pipeline_integration(self):
        """测试完整的处理管道集成"""
        # 创建所有组件
        config_manager = ConfigManager(self.temp_config_file.name)
        data_filter = DataFilter(config_manager.get_filter_rules())
        output_handler = OutputHandler(config_manager.get_output_config())
        statistics = Statistics(config_manager.get_statistics_config())
        
        # 启动统计
        statistics.start()
        
        # 模拟完整的消息处理流程
        processed_count = 0
        output_count = 0
        
        with patch('builtins.print'):  # 避免实际输出
            for message in self.test_messages:
                # 记录消费
                statistics.record_consumed()
                
                # 过滤消息
                passed, details = data_filter.filter_message(message)
                statistics.record_processed()
                processed_count += 1
                
                if passed:
                    # 输出消息
                    output_handler.output_message(message, details)
                    statistics.record_passed()
                    statistics.record_output()
                    output_count += 1
        
        # 验证最终结果
        self.assertEqual(processed_count, 3)
        self.assertEqual(output_count, 2)
        
        # 验证统计一致性
        stats = statistics.get_current_statistics()
        filter_stats = data_filter.get_statistics()
        
        self.assertEqual(stats['total_processed'], filter_stats['total_processed'])
        self.assertEqual(stats['total_passed'], filter_stats['total_passed'])
        self.assertEqual(output_handler.get_output_count(), output_count)
    
    def test_file_output_integration(self):
        """测试文件输出集成"""
        # 修改配置为文件输出
        file_config = self.test_config.copy()
        file_config['output']['type'] = 'file'
        file_config['output']['file_path'] = 'test_output.json'
        
        # 创建临时配置文件
        temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False)
        yaml.dump(file_config, temp_file, default_flow_style=False, allow_unicode=True)
        temp_file.close()
        
        try:
            # 创建组件
            config_manager = ConfigManager(temp_file.name)
            data_filter = DataFilter(config_manager.get_filter_rules())
            output_handler = OutputHandler(config_manager.get_output_config())
            
            # 处理消息
            for message in self.test_messages:
                passed, details = data_filter.filter_message(message)
                if passed:
                    output_handler.output_message(message, details)
            
            # 关闭输出处理器
            output_handler.close()
            
            # 验证文件输出
            self.assertTrue(os.path.exists('test_output.json'))
            
            # 清理输出文件
            if os.path.exists('test_output.json'):
                os.unlink('test_output.json')
                
        finally:
            os.unlink(temp_file.name)
    
    def test_error_handling_integration(self):
        """测试错误处理集成"""
        # 创建包含无效规则的配置
        invalid_config = self.test_config.copy()
        invalid_config['filter']['rules'] = ['invalid rule syntax']
        
        temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False)
        yaml.dump(invalid_config, temp_file, default_flow_style=False, allow_unicode=True)
        temp_file.close()
        
        try:
            config_manager = ConfigManager(temp_file.name)
            
            # 应该抛出异常
            with self.assertRaises(ValueError):
                DataFilter(config_manager.get_filter_rules())
                
        finally:
            os.unlink(temp_file.name)
    
    def test_statistics_reporting_integration(self):
        """测试统计报告集成"""
        # 创建组件
        config_manager = ConfigManager(self.temp_config_file.name)
        data_filter = DataFilter(config_manager.get_filter_rules())
        output_handler = OutputHandler(config_manager.get_output_config())
        statistics = Statistics(config_manager.get_statistics_config())
        
        # 启动统计
        statistics.start()
        
        # 处理消息
        for message in self.test_messages:
            statistics.record_consumed()
            passed, details = data_filter.filter_message(message)
            statistics.record_processed()
            
            if passed:
                statistics.record_passed()
                statistics.record_output()
        
        # 生成综合统计报告
        stats = statistics.get_current_statistics()
        filter_stats = data_filter.get_statistics()
        
        combined_stats = {**stats, **filter_stats}
        
        # 测试统计输出
        with patch('builtins.print') as mock_print:
            output_handler.output_statistics(combined_stats)
            
            # 验证统计输出被调用
            mock_print.assert_called()
    
    def test_configuration_reload_integration(self):
        """测试配置重新加载集成"""
        # 创建初始组件
        config_manager = ConfigManager(self.temp_config_file.name)
        initial_rules = config_manager.get_filter_rules()
        
        # 修改配置文件
        new_config = self.test_config.copy()
        new_config['filter']['rules'] = ['field1 = 999']
        
        with open(self.temp_config_file.name, 'w', encoding='utf-8') as f:
            yaml.dump(new_config, f, default_flow_style=False, allow_unicode=True)
        
        # 重新加载配置
        config_manager.reload_config()
        new_rules = config_manager.get_filter_rules()
        
        # 验证配置已更新
        self.assertNotEqual(initial_rules, new_rules)
        self.assertEqual(new_rules, ['field1 = 999'])


if __name__ == '__main__':
    unittest.main()
