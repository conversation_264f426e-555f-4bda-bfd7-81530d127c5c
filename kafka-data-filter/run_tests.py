#!/usr/bin/env python3
"""
测试运行脚本

运行所有单元测试并生成测试报告
"""

import sys
import unittest
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


def run_all_tests():
    """运行所有测试"""
    # 发现并加载所有测试
    loader = unittest.TestLoader()
    start_dir = 'tests'
    suite = loader.discover(start_dir, pattern='test_*.py')
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 输出测试结果摘要
    print("\n" + "=" * 70)
    print("测试结果摘要")
    print("=" * 70)
    print(f"运行测试数: {result.testsRun}")
    print(f"失败数: {len(result.failures)}")
    print(f"错误数: {len(result.errors)}")
    print(f"跳过数: {len(result.skipped)}")
    
    if result.failures:
        print(f"\n失败的测试:")
        for test, traceback in result.failures:
            print(f"  - {test}")
    
    if result.errors:
        print(f"\n错误的测试:")
        for test, traceback in result.errors:
            print(f"  - {test}")
    
    success_rate = (result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100
    print(f"\n成功率: {success_rate:.1f}%")
    
    return result.wasSuccessful()


def run_specific_test(test_module):
    """运行特定的测试模块"""
    try:
        # 导入测试模块
        module = __import__(f'tests.{test_module}', fromlist=[test_module])
        
        # 创建测试套件
        loader = unittest.TestLoader()
        suite = loader.loadTestsFromModule(module)
        
        # 运行测试
        runner = unittest.TextTestRunner(verbosity=2)
        result = runner.run(suite)
        
        return result.wasSuccessful()
        
    except ImportError as e:
        print(f"无法导入测试模块 {test_module}: {e}")
        return False


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="运行Kafka数据过滤程序测试")
    parser.add_argument(
        '--module', '-m',
        help="运行特定的测试模块（如：test_config_manager）"
    )
    parser.add_argument(
        '--list', '-l',
        action='store_true',
        help="列出所有可用的测试模块"
    )
    
    args = parser.parse_args()
    
    if args.list:
        # 列出所有测试模块
        test_dir = Path('tests')
        test_files = list(test_dir.glob('test_*.py'))
        
        print("可用的测试模块:")
        for test_file in test_files:
            module_name = test_file.stem
            print(f"  - {module_name}")
        return
    
    if args.module:
        # 运行特定测试模块
        print(f"运行测试模块: {args.module}")
        success = run_specific_test(args.module)
    else:
        # 运行所有测试
        print("运行所有测试...")
        success = run_all_tests()
    
    # 根据测试结果设置退出码
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
