# Kafka数据过滤程序示例配置文件
# 这是一个简化的配置示例，适合快速开始使用

# Kafka连接配置
kafka:
  # Kafka服务器地址
  bootstrap_servers: "localhost:9092"

  # 要消费的topic
  topic: "test-topic"

  # 消费者组ID
  group_id: "kafka-filter-demo"

  # 从最新消息开始消费
  auto_offset_reset: "latest"

# 过滤规则配置
filter:
  # 示例过滤规则
  rules:
    # 规则1：过滤重要消息
    - "priority = \"high\" AND status = \"active\""

    # 规则2：过滤特定类型的消息
    - "type IN (\"news\", \"alert\", \"notification\") AND score > 70"

# 输出配置
output:
  # 输出到控制台
  type: "console"
  
  # 使用格式化的JSON输出
  format: "pretty_json"
  
  # 不显示过滤详情（简化输出）
  show_filter_details: false

# 统计配置
statistics:
  # 启用统计
  enabled: true
  
  # 每30秒输出一次统计信息
  interval: 30
  
  # 显示详细统计
  show_details: true

# 日志配置
logging:
  # 信息级别日志
  level: "INFO"
