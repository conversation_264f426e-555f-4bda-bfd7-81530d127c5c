#!/usr/bin/env python3
"""
过滤规则验证测试脚本

基于filter_config.yaml中的过滤规则构造测试数据并验证过滤功能
"""

import sys
import os
import json
from typing import Dict, Any, List

# 添加项目根目录到Python路径
sys.path.insert(0, '.')

from kafka_data_filter.config_manager import ConfigManager
from kafka_data_filter.data_filter import DataFilter


def analyze_filter_rules():
    """分析配置文件中的过滤规则"""
    print("=" * 60)
    print("过滤规则分析")
    print("=" * 60)
    
    config_manager = ConfigManager('filter_config.yaml')
    rules = config_manager.get_filter_rules()
    
    print(f"配置文件中共有 {len(rules)} 个过滤规则：\n")
    
    for i, rule in enumerate(rules, 1):
        print(f"规则 {i}:")
        print(f"  {rule}")
        print()
    
    return rules


def create_test_data() -> List[Dict[str, Any]]:
    """构造测试数据"""
    print("=" * 60)
    print("构造测试数据")
    print("=" * 60)
    
    # 测试数据1：应该通过规则1
    test_data_1 = {
        "y_is_first_gather": 1,
        "n_content_spam": {
            "class2": "时政新闻"  # 不在排除列表中
        },
        "y_yq_focus": {
            "label_name": "关注"
        },
        "n_media": {
            "nature": "境外媒体"
        },
        "title": "重要政治新闻",
        "content": "这是一条重要的政治新闻内容",
        "timestamp": "2025-08-21T10:00:00Z"
    }
    
    # 测试数据2：应该通过规则2
    test_data_2 = {
        "y_is_first_gather": 1,
        "n_content_spam": {
            "class2": "科技资讯"  # 不在排除列表中
        },
        "y_yq_focus": {
            "label_name": "不关注",
            "probability": 0.6  # 小于0.8
        },
        "n_media": {
            "nature": "党媒"
        },
        "title": "科技发展动态",
        "content": "最新科技发展动态报告",
        "timestamp": "2025-08-21T11:00:00Z"
    }
    
    # 测试数据3：应该被过滤掉（不满足任何规则）
    test_data_3 = {
        "y_is_first_gather": 0,  # 不等于1，不满足任何规则
        "n_content_spam": {
            "class2": "广告信息"  # 在排除列表中
        },
        "y_yq_focus": {
            "label_name": "关注"
        },
        "n_media": {
            "nature": "商业媒体"  # 不在允许列表中
        },
        "title": "广告内容",
        "content": "这是一条广告内容",
        "timestamp": "2025-08-21T12:00:00Z"
    }
    
    # 测试数据4：应该被过滤掉（部分条件不满足）
    test_data_4 = {
        "y_is_first_gather": 1,
        "n_content_spam": {
            "class2": "股票资讯"  # 在排除列表中
        },
        "y_yq_focus": {
            "label_name": "关注"
        },
        "n_media": {
            "nature": "境外媒体"
        },
        "title": "股票市场分析",
        "content": "今日股票市场分析报告",
        "timestamp": "2025-08-21T13:00:00Z"
    }
    
    test_cases = [
        ("测试数据1（应该通过规则1）", test_data_1, True),
        ("测试数据2（应该通过规则2）", test_data_2, True),
        ("测试数据3（应该被过滤）", test_data_3, False),
        ("测试数据4（应该被过滤）", test_data_4, False)
    ]
    
    for name, data, expected in test_cases:
        print(f"{name}:")
        print(f"  预期结果: {'通过过滤' if expected else '被过滤'}")
        print(f"  数据内容: {json.dumps(data, ensure_ascii=False, indent=2)}")
        print()
    
    return test_cases


def validate_filter_rules(test_cases: List[tuple]):
    """验证过滤规则"""
    print("=" * 60)
    print("过滤规则验证")
    print("=" * 60)
    
    # 初始化数据过滤器
    config_manager = ConfigManager('filter_config.yaml')
    rules = config_manager.get_filter_rules()
    data_filter = DataFilter(rules)
    
    print(f"使用 {len(rules)} 个过滤规则进行验证...\n")
    
    passed_tests = 0
    total_tests = len(test_cases)
    
    for i, (name, test_data, expected_result) in enumerate(test_cases, 1):
        print(f"测试 {i}: {name}")
        print("-" * 40)
        
        try:
            # 执行过滤
            passed, details = data_filter.filter_message(test_data)
            
            # 检查结果是否符合预期
            if passed == expected_result:
                print(f"✅ 测试通过")
                print(f"   实际结果: {'通过过滤' if passed else '被过滤'}")
                print(f"   预期结果: {'通过过滤' if expected_result else '被过滤'}")
                passed_tests += 1
            else:
                print(f"❌ 测试失败")
                print(f"   实际结果: {'通过过滤' if passed else '被过滤'}")
                print(f"   预期结果: {'通过过滤' if expected_result else '被过滤'}")
            
            # 显示详细的规则执行结果
            print(f"   规则执行详情:")
            for j, rule_result in enumerate(details['rule_results']):
                rule_status = "✅ 通过" if rule_result['passed'] else "❌ 未通过"
                print(f"     规则 {j+1}: {rule_status}")
                if 'error' in rule_result:
                    print(f"     错误: {rule_result['error']}")
            
        except Exception as e:
            print(f"❌ 测试异常: {e}")
        
        print()
    
    # 输出总结
    print("=" * 60)
    print("验证结果总结")
    print("=" * 60)
    print(f"总测试数: {total_tests}")
    print(f"通过测试: {passed_tests}")
    print(f"失败测试: {total_tests - passed_tests}")
    print(f"成功率: {passed_tests/total_tests*100:.1f}%")
    
    if passed_tests == total_tests:
        print("🎉 所有测试通过！过滤规则工作正常。")
    else:
        print("⚠️  部分测试失败，请检查过滤规则或测试数据。")
    
    return passed_tests == total_tests


def test_individual_conditions():
    """测试各个条件的独立验证"""
    print("\n" + "=" * 60)
    print("独立条件测试")
    print("=" * 60)
    
    from kafka_data_filter.filter_parser import FilterParser
    
    parser = FilterParser()
    
    # 测试各个独立条件
    test_conditions = [
        ("y_is_first_gather = 1", {"y_is_first_gather": 1}, True),
        ("y_is_first_gather = 1", {"y_is_first_gather": 0}, False),
        ("n_content_spam.class2 NOT IN (\"广告信息\", \"股票资讯\")", 
         {"n_content_spam": {"class2": "时政新闻"}}, True),
        ("n_content_spam.class2 NOT IN (\"广告信息\", \"股票资讯\")", 
         {"n_content_spam": {"class2": "广告信息"}}, False),
        ("y_yq_focus.label_name = \"关注\"", 
         {"y_yq_focus": {"label_name": "关注"}}, True),
        ("y_yq_focus.label_name = \"关注\"", 
         {"y_yq_focus": {"label_name": "不关注"}}, False),
        ("n_media.nature IN (\"境外媒体\", \"党媒\")", 
         {"n_media": {"nature": "境外媒体"}}, True),
        ("n_media.nature IN (\"境外媒体\", \"党媒\")", 
         {"n_media": {"nature": "商业媒体"}}, False),
        ("y_yq_focus.probability < 0.8", 
         {"y_yq_focus": {"probability": 0.6}}, True),
        ("y_yq_focus.probability < 0.8", 
         {"y_yq_focus": {"probability": 0.9}}, False),
    ]
    
    for condition, test_data, expected in test_conditions:
        try:
            result = parser.filter_data(condition, test_data)
            status = "✅" if result == expected else "❌"
            print(f"{status} {condition}")
            print(f"   数据: {test_data}")
            print(f"   结果: {result}, 预期: {expected}")
        except Exception as e:
            print(f"❌ {condition}")
            print(f"   错误: {e}")
        print()


def main():
    """主函数"""
    print("Kafka数据过滤程序 - 过滤规则验证测试")
    
    try:
        # 1. 分析过滤规则
        rules = analyze_filter_rules()
        
        # 2. 构造测试数据
        test_cases = create_test_data()
        
        # 3. 验证过滤规则
        success = validate_filter_rules(test_cases)
        
        # 4. 测试独立条件
        test_individual_conditions()
        
        return success
        
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
