#!/usr/bin/env python3
"""
测试过滤规则功能

验证修复后的过滤规则解析器能正确处理复杂的过滤表达式
"""

import sys
sys.path.insert(0, '.')

from kafka_data_filter.filter_parser import FilterParser
from kafka_data_filter.data_filter import DataFilter


def test_not_in_operator():
    """测试NOT IN运算符"""
    print("测试 NOT IN 运算符...")
    
    parser = FilterParser()
    test_cases = [
        'field1 NOT IN ("value1", "value2")',
        'field.subfield NOT IN ("广告信息", "股票资讯")',
        'status NOT IN ("deleted", "draft")'
    ]
    
    for expression in test_cases:
        try:
            result = parser.parse(expression)
            print(f"✅ {expression}")
        except Exception as e:
            print(f"❌ {expression}: {e}")
            return False
    
    return True


def test_complex_expressions():
    """测试复杂表达式"""
    print("\n测试复杂表达式...")
    
    parser = FilterParser()
    test_cases = [
        'field1 = 1 AND field2 NOT IN ("a", "b") AND field3 = "test"',
        'y_is_first_gather = 1 AND n_content_spam.class2 NOT IN ("广告信息", "股票资讯")',
        '(priority = "high" OR urgency = "urgent") AND status NOT IN ("deleted", "draft")'
    ]
    
    for expression in test_cases:
        try:
            result = parser.parse(expression)
            print(f"✅ {expression}")
        except Exception as e:
            print(f"❌ {expression}: {e}")
            return False
    
    return True


def test_data_filtering():
    """测试数据过滤功能"""
    print("\n测试数据过滤功能...")
    
    # 测试数据
    test_data = {
        'y_is_first_gather': 1,
        'n_content_spam': {
            'class2': '新闻资讯'
        },
        'y_yq_focus': {
            'label_name': '关注'
        },
        'n_media': {
            'nature': '境外媒体'
        }
    }
    
    # 测试规则
    rules = [
        'y_is_first_gather = 1 AND n_content_spam.class2 NOT IN ("广告信息", "股票资讯") AND y_yq_focus.label_name = "关注" AND n_media.nature IN ("境外媒体", "党媒")'
    ]
    
    try:
        data_filter = DataFilter(rules)
        passed, details = data_filter.filter_message(test_data)
        
        if passed:
            print("✅ 数据过滤测试通过")
            print(f"   过滤结果: {details['passed']}")
            print(f"   规则结果: {[r['passed'] for r in details['rule_results']]}")
        else:
            print("❌ 数据过滤测试失败")
            print(f"   过滤结果: {details['passed']}")
            print(f"   规则结果: {[r['passed'] for r in details['rule_results']]}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 数据过滤测试异常: {e}")
        return False


def test_config_file_rules():
    """测试配置文件中的实际规则"""
    print("\n测试配置文件中的实际规则...")
    
    # 从配置文件中的实际规则
    rules = [
        'y_is_first_gather = 1 AND n_content_spam.class2 NOT IN ("广告信息", "股票资讯", "新闻合集", "历史科普", "明星娱乐") AND y_yq_focus.label_name = "关注" AND n_media.nature IN ("境外媒体", "党媒","政府部门")',
        'y_is_first_gather = 1 AND n_content_spam.class2 NOT IN ("广告信息", "股票资讯", "新闻合集", "历史科普", "明星娱乐") AND y_yq_focus.label_name = "不关注" AND y_yq_focus.probability < 0.8 AND n_media.nature IN ("境外媒体", "党媒","政府部门")'
    ]
    
    try:
        data_filter = DataFilter(rules)
        print(f"✅ 成功解析配置文件中的 {len(rules)} 个规则")
        
        # 获取规则信息
        rules_info = data_filter.get_rules()
        for i, rule_info in enumerate(rules_info):
            print(f"   规则 {i}: {rule_info['rule'][:50]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置文件规则解析失败: {e}")
        return False


def main():
    """运行所有测试"""
    print("=" * 60)
    print("过滤规则功能测试")
    print("=" * 60)
    
    tests = [
        test_not_in_operator,
        test_complex_expressions,
        test_data_filtering,
        test_config_file_rules
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print("-" * 40)
    
    print()
    print("=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    print("=" * 60)
    
    if passed == total:
        print("🎉 所有测试通过！过滤规则功能正常工作。")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关功能。")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
