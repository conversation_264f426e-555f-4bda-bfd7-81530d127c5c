"""
控制台报告器

提供实时的控制台输出功能，显示消费者组监控信息
"""

import os
import sys
import threading
import time
from typing import Dict, List, Optional
from datetime import datetime
from colorama import init, Fore, Back, Style

from ..models.event import Event
from ..utils.formatter import (
    format_consumer_group_table, format_partition_table, format_events_table,
    format_lag_stats, format_timestamp, format_duration, format_number
)

# 初始化colorama
init(autoreset=True)


class ConsoleReporter:
    """
    控制台报告器
    
    负责在控制台实时显示监控信息，包括消费者组状态、事件等
    """
    
    def __init__(self, config: Dict):
        """
        初始化控制台报告器
        
        参数:
            config: 控制台输出配置
        """
        self.config = config
        self.enabled = config.get('enabled', True)
        self.refresh_interval = config.get('refresh_interval', 5)
        self.show_empty_groups = config.get('show_empty_groups', False)
        self.show_stable_groups = config.get('show_stable_groups', True)
        self.max_display_groups = config.get('max_display_groups', 50)
        
        self.current_groups = {}
        self.recent_events = []
        self.monitoring_stats = {}
        
        self.display_thread = None
        self.is_running = False
        self.lock = threading.Lock()
    
    def start(self):
        """
        启动控制台显示
        """
        if not self.enabled or self.is_running:
            return
        
        self.is_running = True
        self.display_thread = threading.Thread(target=self._display_loop, daemon=True)
        self.display_thread.start()
    
    def stop(self):
        """
        停止控制台显示
        """
        self.is_running = False
        if self.display_thread and self.display_thread.is_alive():
            self.display_thread.join(timeout=5)
    
    def update_groups(self, groups_data: Dict[str, Dict]):
        """
        更新消费者组数据
        
        参数:
            groups_data: 消费者组数据
        """
        with self.lock:
            self.current_groups = groups_data.copy()
    
    def add_event(self, event: Event):
        """
        添加事件
        
        参数:
            event: 事件对象
        """
        with self.lock:
            self.recent_events.append(event)
            # 只保留最近50个事件
            if len(self.recent_events) > 50:
                self.recent_events = self.recent_events[-50:]
    
    def update_stats(self, stats: Dict):
        """
        更新监控统计信息
        
        参数:
            stats: 统计信息
        """
        with self.lock:
            self.monitoring_stats = stats.copy()
    
    def _display_loop(self):
        """
        显示循环
        """
        while self.is_running:
            try:
                self._refresh_display()
                time.sleep(self.refresh_interval)
            except KeyboardInterrupt:
                break
            except Exception as e:
                print(f"控制台显示错误: {e}")
                time.sleep(1)
    
    def _refresh_display(self):
        """
        刷新显示内容
        """
        # 清屏
        os.system('cls' if os.name == 'nt' else 'clear')
        
        with self.lock:
            groups_data = self.current_groups.copy()
            events = self.recent_events.copy()
            stats = self.monitoring_stats.copy()
        
        # 显示标题
        self._print_header()
        
        # 显示监控统计
        self._print_monitoring_stats(stats)
        
        # 显示消费者组概览
        self._print_groups_overview(groups_data)
        
        # 显示最近事件
        self._print_recent_events(events)
        
        # 显示详细信息（如果只有少数几个消费者组）
        if len(groups_data) <= 5:
            self._print_detailed_info(groups_data)
    
    def _print_header(self):
        """
        打印标题
        """
        title = "Kafka 消费者组监控器"
        print(f"\n{Fore.CYAN}{Style.BRIGHT}{'=' * 60}")
        print(f"{title:^60}")
        print(f"{'=' * 60}{Style.RESET_ALL}")
        print(f"当前时间: {format_timestamp(datetime.now())}")
    
    def _print_monitoring_stats(self, stats: Dict):
        """
        打印监控统计信息
        
        参数:
            stats: 统计信息
        """
        if not stats:
            return
        
        print(f"\n{Fore.GREEN}{Style.BRIGHT}监控状态{Style.RESET_ALL}")
        print("-" * 40)
        
        status_color = Fore.GREEN if stats.get('is_running') else Fore.RED
        status_text = "运行中" if stats.get('is_running') else "已停止"
        print(f"状态: {status_color}{status_text}{Style.RESET_ALL}")
        
        if stats.get('uptime_seconds'):
            uptime = format_duration(stats['uptime_seconds'])
            print(f"运行时间: {uptime}")
        
        print(f"监控周期: {stats.get('total_cycles', 0)}")
        print(f"监控组数: {stats.get('monitored_groups_count', 0)}")
        print(f"错误次数: {stats.get('errors', 0)}")
        
        if stats.get('last_update'):
            last_update = datetime.fromisoformat(stats['last_update']) if isinstance(stats['last_update'], str) else stats['last_update']
            print(f"最后更新: {format_timestamp(last_update, relative=True)}")
    
    def _print_groups_overview(self, groups_data: Dict[str, Dict]):
        """
        打印消费者组概览
        
        参数:
            groups_data: 消费者组数据
        """
        if not groups_data:
            print(f"\n{Fore.YELLOW}暂无消费者组数据{Style.RESET_ALL}")
            return
        
        print(f"\n{Fore.GREEN}{Style.BRIGHT}消费者组概览{Style.RESET_ALL}")
        print("-" * 40)
        
        # 过滤要显示的消费者组
        filtered_groups = self._filter_groups_for_display(groups_data)
        
        if not filtered_groups:
            print(f"{Fore.YELLOW}没有符合显示条件的消费者组{Style.RESET_ALL}")
            return
        
        # 显示表格
        table = format_consumer_group_table(filtered_groups)
        print(table)
        
        # 显示统计摘要
        self._print_groups_summary(groups_data)
    
    def _print_recent_events(self, events: List[Event]):
        """
        打印最近事件
        
        参数:
            events: 事件列表
        """
        if not events:
            return
        
        print(f"\n{Fore.GREEN}{Style.BRIGHT}最近事件 (最新10条){Style.RESET_ALL}")
        print("-" * 40)
        
        # 按时间倒序排序，显示最新的10个事件
        recent_events = sorted(events, key=lambda e: e.timestamp, reverse=True)[:10]
        
        for event in recent_events:
            severity_color = self._get_severity_color(event.severity.value)
            timestamp_str = format_timestamp(event.timestamp, relative=True)
            
            print(f"{timestamp_str} {severity_color}[{event.severity.value.upper()}]{Style.RESET_ALL} "
                  f"{event.group_id}: {event.message}")
    
    def _print_detailed_info(self, groups_data: Dict[str, Dict]):
        """
        打印详细信息
        
        参数:
            groups_data: 消费者组数据
        """
        for group_id, group_info in list(groups_data.items())[:3]:  # 最多显示3个组的详细信息
            consumer_group = group_info['consumer_group']
            lag_stats = group_info['lag_stats']
            
            print(f"\n{Fore.BLUE}{Style.BRIGHT}消费者组详情: {group_id}{Style.RESET_ALL}")
            print("-" * 50)
            
            # 基本信息
            print(f"状态: {self._get_state_color(consumer_group.state.value)}{consumer_group.state.value}{Style.RESET_ALL}")
            print(f"成员数: {consumer_group.member_count}")
            print(f"主题: {', '.join(consumer_group.topics)}")
            
            # 积压统计
            print(f"\n积压统计:")
            print(format_lag_stats(lag_stats))
            
            # 分区积压排名（前5个）
            if consumer_group.lagging_partitions:
                # 简化的分区排名显示，避免循环导入
                lagging_partitions = sorted(consumer_group.lagging_partitions,
                                          key=lambda p: p.lag, reverse=True)[:5]
                top_partitions = []
                for i, partition in enumerate(lagging_partitions):
                    top_partitions.append({
                        'rank': i + 1,
                        'topic': partition.topic,
                        'partition': partition.partition,
                        'lag': partition.lag,
                        'current_offset': partition.current_offset,
                        'latest_offset': partition.latest_offset,
                        'percentage': round((partition.lag / consumer_group.total_lag) * 100, 2) if consumer_group.total_lag > 0 else 0
                    })
                
                if top_partitions:
                    print(f"\n积压最多的分区:")
                    partition_table = format_partition_table(top_partitions)
                    print(partition_table)
    
    def _filter_groups_for_display(self, groups_data: Dict[str, Dict]) -> Dict[str, Dict]:
        """
        过滤要显示的消费者组
        
        参数:
            groups_data: 消费者组数据
            
        返回值:
            Dict[str, Dict]: 过滤后的消费者组数据
        """
        filtered = {}
        
        for group_id, group_info in groups_data.items():
            consumer_group = group_info['consumer_group']
            
            # 检查是否显示空组
            if not self.show_empty_groups and consumer_group.member_count == 0:
                continue
            
            # 检查是否显示稳定组
            if not self.show_stable_groups and consumer_group.is_stable and consumer_group.total_lag == 0:
                continue
            
            filtered[group_id] = group_info
            
            # 限制显示数量
            if len(filtered) >= self.max_display_groups:
                break
        
        return filtered
    
    def _print_groups_summary(self, groups_data: Dict[str, Dict]):
        """
        打印消费者组统计摘要
        
        参数:
            groups_data: 消费者组数据
        """
        if not groups_data:
            return
        
        total_groups = len(groups_data)
        stable_groups = sum(1 for info in groups_data.values() 
                           if info['consumer_group'].is_stable)
        rebalancing_groups = sum(1 for info in groups_data.values() 
                               if info['consumer_group'].is_rebalancing)
        total_lag = sum(info['lag_stats']['total_lag'] for info in groups_data.values())
        
        print(f"\n统计摘要:")
        print(f"总消费者组: {total_groups}, 稳定: {stable_groups}, 重平衡中: {rebalancing_groups}")
        print(f"总积压: {format_number(total_lag)}")
    
    def _get_severity_color(self, severity: str) -> str:
        """
        获取严重程度对应的颜色
        
        参数:
            severity: 严重程度
            
        返回值:
            str: 颜色代码
        """
        color_map = {
            'info': Fore.BLUE,
            'warning': Fore.YELLOW,
            'error': Fore.RED,
            'critical': Fore.MAGENTA
        }
        return color_map.get(severity.lower(), Fore.WHITE)
    
    def _get_state_color(self, state: str) -> str:
        """
        获取状态对应的颜色
        
        参数:
            state: 状态
            
        返回值:
            str: 颜色代码
        """
        color_map = {
            'Stable': Fore.GREEN,
            'PreparingRebalance': Fore.YELLOW,
            'CompletingRebalance': Fore.YELLOW,
            'Dead': Fore.RED,
            'Empty': Fore.CYAN
        }
        return color_map.get(state, Fore.WHITE)
