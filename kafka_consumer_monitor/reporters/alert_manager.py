"""
告警管理器

负责处理各种告警事件，包括阈值检查、告警发送等功能
"""

import logging
import threading
from typing import Dict, List, Set, Callable, Optional
from datetime import datetime, timedelta

from ..models.event import Event, EventSeverity, EventType

logger = logging.getLogger(__name__)


class AlertManager:
    """
    告警管理器
    
    负责管理告警规则、处理告警事件、防止重复告警等
    """
    
    def __init__(self, config: Dict):
        """
        初始化告警管理器
        
        参数:
            config: 告警配置
        """
        self.config = config
        self.enabled = config.get('enabled', True)
        self.lag_threshold = config.get('lag_threshold', 1000)
        self.rebalance_duration_threshold = config.get('rebalance_duration_threshold', 60)
        self.cooldown_period = config.get('cooldown_period', 300)  # 5分钟冷却期
        
        # 告警状态跟踪
        self.active_alerts = {}  # 活跃告警
        self.alert_history = []  # 告警历史
        self.cooldown_tracker = {}  # 冷却期跟踪
        
        # 告警处理器列表
        self.alert_handlers = []
        
        self.lock = threading.Lock()
    
    def add_alert_handler(self, handler: Callable[[Event], None]):
        """
        添加告警处理器
        
        参数:
            handler: 告警处理函数
        """
        self.alert_handlers.append(handler)
    
    def process_event(self, event: Event):
        """
        处理事件，检查是否需要触发告警
        
        参数:
            event: 事件对象
        """
        if not self.enabled:
            return
        
        # 检查是否为告警事件
        if not self._is_alert_event(event):
            return
        
        # 检查冷却期
        if self._is_in_cooldown(event):
            logger.debug(f"告警在冷却期内，跳过: {event.group_id} - {event.event_type.value}")
            return
        
        # 处理告警
        self._handle_alert(event)
    
    def _is_alert_event(self, event: Event) -> bool:
        """
        检查事件是否为告警事件
        
        参数:
            event: 事件对象
            
        返回值:
            bool: 是否为告警事件
        """
        # 基于严重程度判断
        if event.severity in [EventSeverity.WARNING, EventSeverity.ERROR, EventSeverity.CRITICAL]:
            return True
        
        # 基于事件类型判断
        alert_event_types = [
            EventType.LAG_THRESHOLD_EXCEEDED,
            EventType.REBALANCE_START,
            EventType.MEMBER_LEAVE,
            EventType.GROUP_STATE_CHANGE,
            EventType.CONSUMER_GROUP_DELETED
        ]
        
        return event.event_type in alert_event_types
    
    def _is_in_cooldown(self, event: Event) -> bool:
        """
        检查告警是否在冷却期内
        
        参数:
            event: 事件对象
            
        返回值:
            bool: 是否在冷却期内
        """
        cooldown_key = f"{event.group_id}_{event.event_type.value}"
        
        with self.lock:
            if cooldown_key in self.cooldown_tracker:
                last_alert_time = self.cooldown_tracker[cooldown_key]
                time_since_last = (datetime.now() - last_alert_time).total_seconds()
                
                if time_since_last < self.cooldown_period:
                    return True
        
        return False
    
    def _handle_alert(self, event: Event):
        """
        处理告警事件
        
        参数:
            event: 事件对象
        """
        try:
            with self.lock:
                # 记录告警
                self._record_alert(event)
                
                # 更新冷却期跟踪
                cooldown_key = f"{event.group_id}_{event.event_type.value}"
                self.cooldown_tracker[cooldown_key] = datetime.now()
            
            # 调用告警处理器
            for handler in self.alert_handlers:
                try:
                    handler(event)
                except Exception as e:
                    logger.error(f"告警处理器执行失败: {e}")
            
            logger.info(f"告警已处理: {event.group_id} - {event.message}")
            
        except Exception as e:
            logger.error(f"处理告警失败: {e}")
    
    def _record_alert(self, event: Event):
        """
        记录告警到历史记录
        
        参数:
            event: 事件对象
        """
        alert_record = {
            'event': event,
            'triggered_at': datetime.now(),
            'resolved_at': None,
            'status': 'active'
        }
        
        # 添加到活跃告警
        alert_key = f"{event.group_id}_{event.event_type.value}"
        self.active_alerts[alert_key] = alert_record
        
        # 添加到历史记录
        self.alert_history.append(alert_record)
        
        # 限制历史记录大小
        if len(self.alert_history) > 1000:
            self.alert_history = self.alert_history[-1000:]
    
    def resolve_alert(self, group_id: str, event_type: EventType):
        """
        解决告警
        
        参数:
            group_id: 消费者组ID
            event_type: 事件类型
        """
        alert_key = f"{group_id}_{event_type.value}"
        
        with self.lock:
            if alert_key in self.active_alerts:
                alert_record = self.active_alerts[alert_key]
                alert_record['resolved_at'] = datetime.now()
                alert_record['status'] = 'resolved'
                
                # 从活跃告警中移除
                del self.active_alerts[alert_key]
                
                logger.info(f"告警已解决: {group_id} - {event_type.value}")
    
    def get_active_alerts(self) -> List[Dict]:
        """
        获取活跃告警列表
        
        返回值:
            List[Dict]: 活跃告警列表
        """
        with self.lock:
            return [
                {
                    'group_id': alert['event'].group_id,
                    'event_type': alert['event'].event_type.value,
                    'severity': alert['event'].severity.value,
                    'message': alert['event'].message,
                    'triggered_at': alert['triggered_at'],
                    'duration': (datetime.now() - alert['triggered_at']).total_seconds()
                }
                for alert in self.active_alerts.values()
            ]
    
    def get_alert_history(self, hours: int = 24) -> List[Dict]:
        """
        获取告警历史记录
        
        参数:
            hours: 查询的时间范围（小时）
            
        返回值:
            List[Dict]: 告警历史列表
        """
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        with self.lock:
            filtered_history = [
                alert for alert in self.alert_history
                if alert['triggered_at'] > cutoff_time
            ]
        
        return [
            {
                'group_id': alert['event'].group_id,
                'event_type': alert['event'].event_type.value,
                'severity': alert['event'].severity.value,
                'message': alert['event'].message,
                'triggered_at': alert['triggered_at'],
                'resolved_at': alert['resolved_at'],
                'status': alert['status'],
                'duration': (
                    (alert['resolved_at'] or datetime.now()) - alert['triggered_at']
                ).total_seconds()
            }
            for alert in filtered_history
        ]
    
    def get_alert_stats(self) -> Dict:
        """
        获取告警统计信息
        
        返回值:
            Dict: 告警统计信息
        """
        with self.lock:
            active_count = len(self.active_alerts)
            
            # 按严重程度统计活跃告警
            severity_counts = {}
            for alert in self.active_alerts.values():
                severity = alert['event'].severity.value
                severity_counts[severity] = severity_counts.get(severity, 0) + 1
            
            # 统计最近24小时的告警
            cutoff_time = datetime.now() - timedelta(hours=24)
            recent_alerts = [
                alert for alert in self.alert_history
                if alert['triggered_at'] > cutoff_time
            ]
            
            # 按消费者组统计
            group_counts = {}
            for alert in recent_alerts:
                group_id = alert['event'].group_id
                group_counts[group_id] = group_counts.get(group_id, 0) + 1
        
        return {
            'active_alerts': active_count,
            'severity_distribution': severity_counts,
            'recent_24h_count': len(recent_alerts),
            'top_alerting_groups': sorted(
                group_counts.items(), 
                key=lambda x: x[1], 
                reverse=True
            )[:10],
            'cooldown_period': self.cooldown_period,
            'enabled': self.enabled
        }
    
    def cleanup_old_data(self):
        """
        清理过期数据
        """
        cutoff_time = datetime.now() - timedelta(hours=48)
        
        with self.lock:
            # 清理告警历史
            self.alert_history = [
                alert for alert in self.alert_history
                if alert['triggered_at'] > cutoff_time
            ]
            
            # 清理冷却期跟踪
            expired_cooldowns = []
            for key, last_time in self.cooldown_tracker.items():
                if (datetime.now() - last_time).total_seconds() > self.cooldown_period * 2:
                    expired_cooldowns.append(key)
            
            for key in expired_cooldowns:
                del self.cooldown_tracker[key]
    
    def update_thresholds(self, lag_threshold: Optional[int] = None, 
                         rebalance_duration_threshold: Optional[int] = None):
        """
        更新告警阈值
        
        参数:
            lag_threshold: 积压告警阈值
            rebalance_duration_threshold: 重平衡持续时间告警阈值
        """
        if lag_threshold is not None:
            self.lag_threshold = lag_threshold
            logger.info(f"积压告警阈值已更新为: {lag_threshold}")
        
        if rebalance_duration_threshold is not None:
            self.rebalance_duration_threshold = rebalance_duration_threshold
            logger.info(f"重平衡持续时间告警阈值已更新为: {rebalance_duration_threshold}秒")


class ConsoleAlertHandler:
    """
    控制台告警处理器
    
    将告警信息输出到控制台
    """
    
    def __init__(self):
        """
        初始化控制台告警处理器
        """
        pass
    
    def __call__(self, event: Event):
        """
        处理告警事件
        
        参数:
            event: 事件对象
        """
        severity_symbols = {
            'info': 'ℹ️',
            'warning': '⚠️',
            'error': '❌',
            'critical': '🚨'
        }
        
        symbol = severity_symbols.get(event.severity.value, '📢')
        timestamp = event.timestamp.strftime('%H:%M:%S')
        
        print(f"\n{symbol} [{timestamp}] 告警: {event.group_id}")
        print(f"   类型: {event.event_type.value}")
        print(f"   严重程度: {event.severity.value.upper()}")
        print(f"   消息: {event.message}")
        
        if event.details:
            print(f"   详情: {event.details}")


class FileAlertHandler:
    """
    文件告警处理器
    
    将告警信息写入文件
    """
    
    def __init__(self, alert_file: str):
        """
        初始化文件告警处理器
        
        参数:
            alert_file: 告警文件路径
        """
        self.alert_file = alert_file
    
    def __call__(self, event: Event):
        """
        处理告警事件
        
        参数:
            event: 事件对象
        """
        try:
            import json
            
            alert_data = {
                'timestamp': event.timestamp.isoformat(),
                'group_id': event.group_id,
                'event_type': event.event_type.value,
                'severity': event.severity.value,
                'message': event.message,
                'details': event.details
            }
            
            alert_line = json.dumps(alert_data, ensure_ascii=False, separators=(',', ':'))
            
            with open(self.alert_file, 'a', encoding='utf-8') as f:
                f.write(alert_line + '\n')
                
        except Exception as e:
            logger.error(f"写入告警文件失败: {e}")
