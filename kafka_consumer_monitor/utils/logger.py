"""
日志工具模块

提供统一的日志配置和管理功能
"""

import logging
import logging.handlers
import os
from pathlib import Path
from typing import Dict, Any


def setup_logging(config: Dict[str, Any]):
    """
    根据配置设置日志系统
    
    参数:
        config: 日志配置字典
    """
    # 获取根日志记录器
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, config.get('level', 'INFO')))
    
    # 清除现有的处理器
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # 设置日志格式
    formatter = logging.Formatter(
        config.get('format', '%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    )
    
    # 控制台日志处理器
    console_config = config.get('console', {})
    if console_config.get('enabled', True):
        console_handler = logging.StreamHandler()
        console_handler.setLevel(getattr(logging, console_config.get('level', 'INFO')))
        console_handler.setFormatter(formatter)
        root_logger.addHandler(console_handler)
    
    # 文件日志处理器
    file_config = config.get('file', {})
    if file_config.get('enabled', True):
        log_file = file_config.get('filename', 'kafka_monitor.log')
        
        # 确保日志目录存在
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 使用RotatingFileHandler支持日志轮转
        max_bytes = file_config.get('max_size_mb', 50) * 1024 * 1024
        backup_count = file_config.get('backup_count', 3)
        
        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=max_bytes,
            backupCount=backup_count,
            encoding='utf-8'
        )
        file_handler.setLevel(getattr(logging, config.get('level', 'INFO')))
        file_handler.setFormatter(formatter)
        root_logger.addHandler(file_handler)


def get_logger(name: str) -> logging.Logger:
    """
    获取指定名称的日志记录器
    
    参数:
        name: 日志记录器名称
        
    返回值:
        logging.Logger: 日志记录器实例
    """
    return logging.getLogger(name)
