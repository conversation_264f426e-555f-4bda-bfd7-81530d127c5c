"""
格式化工具模块

提供各种数据格式化和显示功能
"""

from typing import Dict, List, Any
from datetime import datetime, timedelta
from tabulate import tabulate
import json


def format_bytes(bytes_value: int) -> str:
    """
    格式化字节数为人类可读的格式
    
    参数:
        bytes_value: 字节数
        
    返回值:
        str: 格式化后的字符串
    """
    if bytes_value == 0:
        return "0 B"
    
    units = ['B', 'KB', 'MB', 'GB', 'TB']
    unit_index = 0
    size = float(bytes_value)
    
    while size >= 1024 and unit_index < len(units) - 1:
        size /= 1024
        unit_index += 1
    
    return f"{size:.1f} {units[unit_index]}"


def format_duration(seconds: float) -> str:
    """
    格式化持续时间为人类可读的格式
    
    参数:
        seconds: 秒数
        
    返回值:
        str: 格式化后的字符串
    """
    if seconds < 60:
        return f"{seconds:.1f}s"
    elif seconds < 3600:
        minutes = seconds / 60
        return f"{minutes:.1f}m"
    elif seconds < 86400:
        hours = seconds / 3600
        return f"{hours:.1f}h"
    else:
        days = seconds / 86400
        return f"{days:.1f}d"


def format_timestamp(timestamp: datetime, relative: bool = False) -> str:
    """
    格式化时间戳
    
    参数:
        timestamp: 时间戳
        relative: 是否显示相对时间
        
    返回值:
        str: 格式化后的时间字符串
    """
    if relative:
        now = datetime.now()
        if timestamp.date() == now.date():
            return timestamp.strftime("%H:%M:%S")
        else:
            delta = now - timestamp
            if delta.days == 1:
                return f"昨天 {timestamp.strftime('%H:%M:%S')}"
            elif delta.days < 7:
                return f"{delta.days}天前"
            else:
                return timestamp.strftime("%m-%d %H:%M")
    else:
        return timestamp.strftime("%Y-%m-%d %H:%M:%S")


def format_number(number: int) -> str:
    """
    格式化数字，添加千位分隔符
    
    参数:
        number: 数字
        
    返回值:
        str: 格式化后的数字字符串
    """
    return f"{number:,}"


def format_percentage(value: float, total: float) -> str:
    """
    格式化百分比
    
    参数:
        value: 数值
        total: 总数
        
    返回值:
        str: 百分比字符串
    """
    if total == 0:
        return "0.0%"
    
    percentage = (value / total) * 100
    return f"{percentage:.1f}%"


def format_consumer_group_table(groups_data: Dict[str, Dict]) -> str:
    """
    格式化消费者组信息为表格
    
    参数:
        groups_data: 消费者组数据字典
        
    返回值:
        str: 格式化后的表格字符串
    """
    if not groups_data:
        return "暂无消费者组数据"
    
    headers = ["消费者组", "状态", "成员数", "总积压", "主题数", "最后更新"]
    rows = []
    
    for group_id, group_info in groups_data.items():
        consumer_group = group_info['consumer_group']
        lag_stats = group_info['lag_stats']
        
        rows.append([
            group_id,
            consumer_group.state.value,
            consumer_group.member_count,
            format_number(lag_stats['total_lag']),
            len(consumer_group.topics),
            format_timestamp(consumer_group.last_updated, relative=True)
        ])
    
    return tabulate(rows, headers=headers, tablefmt="grid")


def format_partition_table(partitions: List[Dict]) -> str:
    """
    格式化分区信息为表格
    
    参数:
        partitions: 分区信息列表
        
    返回值:
        str: 格式化后的表格字符串
    """
    if not partitions:
        return "暂无分区数据"
    
    headers = ["排名", "主题", "分区", "积压", "当前Offset", "最新Offset", "占比"]
    rows = []
    
    for partition in partitions:
        rows.append([
            partition['rank'],
            partition['topic'],
            partition['partition'],
            format_number(partition['lag']),
            format_number(partition['current_offset']) if partition['current_offset'] else 'N/A',
            format_number(partition['latest_offset']) if partition['latest_offset'] else 'N/A',
            f"{partition['percentage']:.1f}%"
        ])
    
    return tabulate(rows, headers=headers, tablefmt="grid")


def format_events_table(events: List[Dict]) -> str:
    """
    格式化事件信息为表格
    
    参数:
        events: 事件信息列表
        
    返回值:
        str: 格式化后的表格字符串
    """
    if not events:
        return "暂无事件数据"
    
    headers = ["时间", "消费者组", "事件类型", "严重程度", "消息"]
    rows = []
    
    for event in events:
        if hasattr(event, 'to_dict'):
            event_dict = event.to_dict()
        else:
            event_dict = event
        
        rows.append([
            format_timestamp(datetime.fromisoformat(event_dict['timestamp']), relative=True),
            event_dict['group_id'],
            event_dict['event_type'],
            event_dict['severity'],
            event_dict['message'][:50] + "..." if len(event_dict['message']) > 50 else event_dict['message']
        ])
    
    return tabulate(rows, headers=headers, tablefmt="grid")


def format_lag_stats(lag_stats: Dict[str, Any]) -> str:
    """
    格式化积压统计信息
    
    参数:
        lag_stats: 积压统计数据
        
    返回值:
        str: 格式化后的统计信息
    """
    lines = [
        f"总积压: {format_number(lag_stats['total_lag'])}",
        f"最大分区积压: {format_number(lag_stats['max_partition_lag'])}",
        f"平均分区积压: {lag_stats['avg_partition_lag']:.1f}",
        f"有积压的分区: {lag_stats['lagging_partitions_count']}/{lag_stats['total_partitions']}"
    ]
    
    if lag_stats.get('lag_by_topic'):
        lines.append("\n按主题统计:")
        for topic, stats in lag_stats['lag_by_topic'].items():
            lines.append(f"  {topic}: {format_number(stats['total_lag'])} "
                        f"({stats['lagging_partitions']}/{stats['partition_count']} 分区)")
    
    return "\n".join(lines)


def format_json(data: Any, indent: int = 2) -> str:
    """
    格式化JSON数据
    
    参数:
        data: 要格式化的数据
        indent: 缩进空格数
        
    返回值:
        str: 格式化后的JSON字符串
    """
    def json_serializer(obj):
        """JSON序列化器，处理datetime等特殊类型"""
        if isinstance(obj, datetime):
            return obj.isoformat()
        elif hasattr(obj, 'to_dict'):
            return obj.to_dict()
        elif hasattr(obj, '__dict__'):
            return obj.__dict__
        else:
            return str(obj)
    
    return json.dumps(data, ensure_ascii=False, indent=indent, default=json_serializer)


def truncate_string(text: str, max_length: int = 50, suffix: str = "...") -> str:
    """
    截断字符串
    
    参数:
        text: 原始字符串
        max_length: 最大长度
        suffix: 截断后缀
        
    返回值:
        str: 截断后的字符串
    """
    if len(text) <= max_length:
        return text
    
    return text[:max_length - len(suffix)] + suffix


def format_health_status(is_healthy: bool, details: str = "") -> str:
    """
    格式化健康状态
    
    参数:
        is_healthy: 是否健康
        details: 详细信息
        
    返回值:
        str: 格式化后的状态字符串
    """
    status = "✅ 健康" if is_healthy else "❌ 异常"
    return f"{status} {details}".strip()
