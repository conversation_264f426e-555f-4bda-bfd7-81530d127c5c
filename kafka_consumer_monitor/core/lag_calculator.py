"""
Lag计算器

提供消费者组积压计算和分析功能
"""

import logging
from typing import List, Dict, Optional
from datetime import datetime, timedelta

from ..models.consumer_group import ConsumerGroup
from ..models.partition_info import PartitionInfo


logger = logging.getLogger(__name__)


class LagCalculator:
    """
    Lag计算器
    
    负责计算和分析消费者组的积压情况，提供各种积压统计和分析功能
    """
    
    def __init__(self):
        """
        初始化Lag计算器
        """
        self.historical_lags = {}  # 存储历史积压数据
    
    def calculate_group_lag(self, consumer_group: ConsumerGroup) -> Dict[str, any]:
        """
        计算消费者组的积压统计信息
        
        参数:
            consumer_group: 消费者组对象
            
        返回值:
            Dict[str, any]: 积压统计信息
        """
        if not consumer_group.partitions:
            return {
                'total_lag': 0,
                'max_partition_lag': 0,
                'avg_partition_lag': 0,
                'lagging_partitions_count': 0,
                'total_partitions': 0,
                'lag_by_topic': {},
                'lag_distribution': {}
            }
        
        # 计算基本统计
        total_lag = consumer_group.total_lag
        partition_lags = [p.lag for p in consumer_group.partitions if p.lag is not None]
        
        max_partition_lag = max(partition_lags) if partition_lags else 0
        avg_partition_lag = sum(partition_lags) / len(partition_lags) if partition_lags else 0
        lagging_partitions_count = len(consumer_group.lagging_partitions)
        
        # 按主题统计积压
        lag_by_topic = {}
        for topic in consumer_group.topics:
            topic_partitions = consumer_group.get_partition_by_topic(topic)
            topic_lag = sum(p.lag for p in topic_partitions if p.lag is not None)
            lag_by_topic[topic] = {
                'total_lag': topic_lag,
                'partition_count': len(topic_partitions),
                'lagging_partitions': len([p for p in topic_partitions if p.has_lag])
            }
        
        # 积压分布统计
        lag_distribution = self._calculate_lag_distribution(partition_lags)
        
        # 存储历史数据
        self._store_historical_lag(consumer_group.group_id, total_lag)
        
        return {
            'total_lag': total_lag,
            'max_partition_lag': max_partition_lag,
            'avg_partition_lag': round(avg_partition_lag, 2),
            'lagging_partitions_count': lagging_partitions_count,
            'total_partitions': len(consumer_group.partitions),
            'lag_by_topic': lag_by_topic,
            'lag_distribution': lag_distribution
        }
    
    def _calculate_lag_distribution(self, partition_lags: List[int]) -> Dict[str, int]:
        """
        计算积压分布统计
        
        参数:
            partition_lags: 分区积压列表
            
        返回值:
            Dict[str, int]: 积压分布统计
        """
        if not partition_lags:
            return {}
        
        # 定义积压区间
        ranges = [
            ('0', 0, 0),
            ('1-100', 1, 100),
            ('101-1000', 101, 1000),
            ('1001-10000', 1001, 10000),
            ('10000+', 10001, float('inf'))
        ]
        
        distribution = {}
        for range_name, min_val, max_val in ranges:
            count = sum(1 for lag in partition_lags 
                       if min_val <= lag <= max_val)
            if count > 0:
                distribution[range_name] = count
        
        return distribution
    
    def _store_historical_lag(self, group_id: str, total_lag: int):
        """
        存储历史积压数据
        
        参数:
            group_id: 消费者组ID
            total_lag: 总积压数量
        """
        now = datetime.now()
        
        if group_id not in self.historical_lags:
            self.historical_lags[group_id] = []
        
        # 添加当前数据点
        self.historical_lags[group_id].append({
            'timestamp': now,
            'lag': total_lag
        })
        
        # 清理过期数据（保留24小时内的数据）
        cutoff_time = now - timedelta(hours=24)
        self.historical_lags[group_id] = [
            data for data in self.historical_lags[group_id]
            if data['timestamp'] > cutoff_time
        ]
    
    def get_lag_trend(self, group_id: str, hours: int = 1) -> Dict[str, any]:
        """
        获取消费者组的积压趋势
        
        参数:
            group_id: 消费者组ID
            hours: 分析的时间范围（小时）
            
        返回值:
            Dict[str, any]: 积压趋势信息
        """
        if group_id not in self.historical_lags:
            return {'trend': 'unknown', 'change': 0, 'data_points': 0}
        
        now = datetime.now()
        cutoff_time = now - timedelta(hours=hours)
        
        # 获取指定时间范围内的数据
        recent_data = [
            data for data in self.historical_lags[group_id]
            if data['timestamp'] > cutoff_time
        ]
        
        if len(recent_data) < 2:
            return {'trend': 'insufficient_data', 'change': 0, 'data_points': len(recent_data)}
        
        # 计算趋势
        first_lag = recent_data[0]['lag']
        last_lag = recent_data[-1]['lag']
        change = last_lag - first_lag
        
        # 计算平均变化率
        time_span = (recent_data[-1]['timestamp'] - recent_data[0]['timestamp']).total_seconds() / 3600
        change_rate = change / time_span if time_span > 0 else 0
        
        # 判断趋势
        if abs(change) < 10:  # 变化很小，认为是稳定
            trend = 'stable'
        elif change > 0:
            trend = 'increasing'
        else:
            trend = 'decreasing'
        
        return {
            'trend': trend,
            'change': change,
            'change_rate_per_hour': round(change_rate, 2),
            'data_points': len(recent_data),
            'time_span_hours': round(time_span, 2)
        }
    
    def detect_lag_anomalies(self, group_id: str, threshold_multiplier: float = 2.0) -> List[Dict[str, any]]:
        """
        检测积压异常
        
        参数:
            group_id: 消费者组ID
            threshold_multiplier: 异常检测阈值倍数
            
        返回值:
            List[Dict[str, any]]: 检测到的异常列表
        """
        if group_id not in self.historical_lags:
            return []
        
        data = self.historical_lags[group_id]
        if len(data) < 10:  # 数据点太少，无法进行异常检测
            return []
        
        # 计算平均值和标准差
        lags = [d['lag'] for d in data]
        avg_lag = sum(lags) / len(lags)
        variance = sum((lag - avg_lag) ** 2 for lag in lags) / len(lags)
        std_dev = variance ** 0.5
        
        # 检测异常点
        anomalies = []
        threshold = avg_lag + threshold_multiplier * std_dev
        
        for data_point in data[-5:]:  # 检查最近5个数据点
            if data_point['lag'] > threshold:
                anomalies.append({
                    'timestamp': data_point['timestamp'],
                    'lag': data_point['lag'],
                    'threshold': threshold,
                    'severity': 'high' if data_point['lag'] > threshold * 1.5 else 'medium'
                })
        
        return anomalies
    
    def get_partition_lag_ranking(self, consumer_group: ConsumerGroup, top_n: int = 10) -> List[Dict[str, any]]:
        """
        获取分区积压排名
        
        参数:
            consumer_group: 消费者组对象
            top_n: 返回前N个分区
            
        返回值:
            List[Dict[str, any]]: 分区积压排名列表
        """
        # 过滤有积压的分区并排序
        lagging_partitions = [
            p for p in consumer_group.partitions 
            if p.lag is not None and p.lag > 0
        ]
        
        # 按积压数量降序排序
        lagging_partitions.sort(key=lambda p: p.lag, reverse=True)
        
        # 构建排名列表
        ranking = []
        for i, partition in enumerate(lagging_partitions[:top_n]):
            ranking.append({
                'rank': i + 1,
                'topic': partition.topic,
                'partition': partition.partition,
                'lag': partition.lag,
                'current_offset': partition.current_offset,
                'latest_offset': partition.latest_offset,
                'consumer_id': partition.consumer_id,
                'percentage': round((partition.lag / consumer_group.total_lag) * 100, 2) if consumer_group.total_lag > 0 else 0
            })
        
        return ranking
