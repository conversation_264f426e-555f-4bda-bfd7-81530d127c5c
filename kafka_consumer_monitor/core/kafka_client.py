"""
Kafka客户端封装

提供Kafka集群连接和操作的封装，包括消费者组信息查询、offset获取等功能
"""

import logging
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime

from kafka import KafkaConsumer, KafkaAdminClient
from kafka.structs import TopicPartition, OffsetAndMetadata
from kafka.errors import KafkaError, NoBrokersAvailable, KafkaTimeoutError

from ..models.consumer_group import ConsumerGroup, ConsumerGroupState, ConsumerMember
from ..models.partition_info import PartitionInfo


logger = logging.getLogger(__name__)


class KafkaClient:
    """
    Kafka客户端封装类

    提供与Kafka集群交互的高级接口，包括消费者组管理、offset查询等功能
    """

    def __init__(self, kafka_config: Dict[str, Any]):
        """
        初始化Kafka客户端

        参数:
            kafka_config: Kafka连接配置
        """
        self.kafka_config = kafka_config
        self.admin_client = None
        self.consumer = None
        self._connect()

    def _connect(self):
        """
        建立Kafka连接

        创建AdminClient和Consumer实例用于不同的操作
        """
        try:
            # 创建AdminClient用于管理操作
            self.admin_client = KafkaAdminClient(**self.kafka_config)

            # 创建Consumer用于offset查询
            consumer_config = self.kafka_config.copy()
            consumer_config['group_id'] = 'kafka_monitor_temp'
            consumer_config['enable_auto_commit'] = False
            consumer_config['auto_offset_reset'] = 'earliest'

            self.consumer = KafkaConsumer(**consumer_config)

            logger.info("Kafka客户端连接成功")

        except NoBrokersAvailable:
            logger.error("无法连接到Kafka集群，请检查bootstrap_servers配置")
            raise
        except Exception as e:
            logger.error(f"Kafka客户端连接失败: {e}")
            raise
    
    def get_consumer_groups(self) -> List[str]:
        """
        获取所有消费者组列表

        返回值:
            List[str]: 消费者组ID列表
        """
        try:
            # 使用AdminClient获取消费者组列表
            # list_consumer_groups()返回tuple列表，每个tuple包含(group_name, protocol_type)
            group_tuples = self.admin_client.list_consumer_groups()
            return [group_tuple[0] for group_tuple in group_tuples]

        except KafkaTimeoutError:
            logger.warning("获取消费者组列表超时")
            return []
        except Exception as e:
            logger.error(f"获取消费者组列表失败: {e}")
            return []
    
    def get_consumer_group_info(self, group_id: str) -> Optional[ConsumerGroup]:
        """
        获取指定消费者组的详细信息

        参数:
            group_id: 消费者组ID

        返回值:
            Optional[ConsumerGroup]: 消费者组信息，获取失败时返回None
        """
        try:
            # 获取消费者组描述信息
            # describe_consumer_groups返回列表，不是字典
            group_descriptions = self.admin_client.describe_consumer_groups([group_id])

            if not group_descriptions:
                logger.warning(f"消费者组 {group_id} 不存在")
                return None

            # 查找匹配的消费者组
            group_desc = None
            for desc in group_descriptions:
                if desc.group == group_id:  # 注意：属性名是'group'，不是'group_id'
                    group_desc = desc
                    break

            if group_desc is None:
                logger.warning(f"消费者组 {group_id} 不存在")
                return None

            # 转换状态枚举
            state = self._convert_group_state(group_desc.state)

            # 构建成员列表
            members = []
            for member in group_desc.members:
                consumer_member = ConsumerMember(
                    member_id=member.member_id,
                    client_id=member.client_id,
                    client_host=member.client_host,
                    member_metadata=member.member_metadata,
                    member_assignment=member.member_assignment
                )
                members.append(consumer_member)

            # 获取分区分配和offset信息
            partitions = self._get_group_partition_info(group_id)

            # 创建消费者组对象
            # 注意：GroupInformation对象没有coordinator属性
            consumer_group = ConsumerGroup(
                group_id=group_id,
                state=state,
                protocol_type=group_desc.protocol_type,
                protocol=group_desc.protocol,
                members=members,
                partitions=partitions,
                coordinator_id=None,  # kafka-python-ng的GroupInformation没有coordinator信息
                last_updated=datetime.now()
            )

            return consumer_group

        except Exception as e:
            logger.error(f"获取消费者组 {group_id} 信息失败: {e}")
            return None
    
    def _convert_group_state(self, kafka_state: str) -> ConsumerGroupState:
        """
        转换Kafka消费者组状态到内部枚举
        
        参数:
            kafka_state: Kafka原始状态字符串
            
        返回值:
            ConsumerGroupState: 内部状态枚举
        """
        state_mapping = {
            'Stable': ConsumerGroupState.STABLE,
            'PreparingRebalance': ConsumerGroupState.PREPARING_REBALANCE,
            'CompletingRebalance': ConsumerGroupState.COMPLETING_REBALANCE,
            'Dead': ConsumerGroupState.DEAD,
            'Empty': ConsumerGroupState.EMPTY
        }
        
        return state_mapping.get(kafka_state, ConsumerGroupState.UNKNOWN)
    
    def _get_group_partition_info(self, group_id: str) -> List[PartitionInfo]:
        """
        获取消费者组的分区分配和offset信息

        参数:
            group_id: 消费者组ID

        返回值:
            List[PartitionInfo]: 分区信息列表
        """
        partitions = []

        try:
            # 获取消费者组的offset信息
            group_offsets = self.admin_client.list_consumer_group_offsets(group_id)

            if not group_offsets:
                return partitions

            # 获取分区的最新offset
            topic_partitions = list(group_offsets.keys())
            latest_offsets = self.consumer.end_offsets(topic_partitions)

            # 获取消费者组成员的分区分配信息
            member_assignments = self._get_member_assignments(group_id)

            for tp, offset_metadata in group_offsets.items():
                current_offset = offset_metadata.offset if offset_metadata else None
                latest_offset = latest_offsets.get(tp)

                # 由于分区分配信息获取的复杂性，这里使用简化处理
                consumer_info = {}

                partition_info = PartitionInfo(
                    topic=tp.topic,
                    partition=tp.partition,
                    current_offset=current_offset,
                    latest_offset=latest_offset,
                    lag=None,  # 将在__post_init__中计算
                    consumer_id=consumer_info.get('consumer_id'),
                    client_id=consumer_info.get('client_id'),
                    host=consumer_info.get('host'),
                    last_updated=datetime.now()
                )

                partitions.append(partition_info)

        except Exception as e:
            logger.error(f"获取消费者组 {group_id} 分区信息失败: {e}")

        return partitions
    
    def _get_member_assignments(self, group_id: str) -> Dict[str, Dict[str, str]]:
        """
        获取消费者组成员的分区分配信息

        参数:
            group_id: 消费者组ID

        返回值:
            Dict[str, Dict[str, str]]: 成员ID到成员信息的映射
        """
        assignments = {}

        try:
            group_descriptions = self.admin_client.describe_consumer_groups([group_id])

            if not group_descriptions:
                return assignments

            # 查找匹配的消费者组
            group_desc = None
            for desc in group_descriptions:
                if desc.group == group_id:
                    group_desc = desc
                    break

            if group_desc is None:
                return assignments

            # 由于kafka-python在分区分配解析方面的限制
            # 这里采用简化的方式，通过成员信息推断分区分配
            for member in group_desc.members:
                # 为每个成员创建基本信息，实际分区分配需要通过其他方式获取
                member_info = {
                    'consumer_id': member.member_id,
                    'client_id': member.client_id,
                    'host': member.client_host
                }
                assignments[member.member_id] = member_info

        except Exception as e:
            logger.error(f"获取消费者组 {group_id} 成员分配信息失败: {e}")

        return assignments
    
    def get_topic_partitions(self, topic: str) -> List[int]:
        """
        获取指定主题的分区列表

        参数:
            topic: 主题名称

        返回值:
            List[int]: 分区编号列表
        """
        try:
            # 使用consumer获取主题的分区信息
            partitions_metadata = self.consumer.partitions_for_topic(topic)
            if partitions_metadata:
                return sorted(list(partitions_metadata))
            else:
                logger.warning(f"主题 {topic} 不存在或没有分区")
                return []

        except Exception as e:
            logger.error(f"获取主题 {topic} 分区信息失败: {e}")
            return []
    
    def close(self):
        """
        关闭Kafka客户端连接
        """
        try:
            if self.consumer:
                self.consumer.close()
            if self.admin_client:
                self.admin_client.close()
            logger.info("Kafka客户端连接已关闭")
        except Exception as e:
            logger.error(f"关闭Kafka客户端连接失败: {e}")
    
    def __enter__(self):
        """
        上下文管理器入口
        """
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """
        上下文管理器出口
        """
        self.close()
