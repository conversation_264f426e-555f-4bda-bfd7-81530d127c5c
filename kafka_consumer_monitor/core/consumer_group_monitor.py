"""
消费者组监控器

核心监控逻辑，协调各个组件完成消费者组的监控任务
"""

import logging
import threading
import time
from datetime import datetime
from typing import Dict, List, Optional, Callable

from .event_tracker import EventTracker
from .kafka_client import KafkaClient
from .lag_calculator import LagCalculator
from ..config.config_loader import ConfigLoader
from ..models.event import Event

logger = logging.getLogger(__name__)


class ConsumerGroupMonitor:
    """
    消费者组监控器
    
    协调Kafka客户端、Lag计算器、事件跟踪器等组件，实现完整的监控功能
    """
    
    def __init__(self, config_loader: ConfigLoader):
        """
        初始化消费者组监控器
        
        参数:
            config_loader: 配置加载器
        """
        self.config_loader = config_loader
        self.kafka_client = None
        self.lag_calculator = LagCalculator()
        self.event_tracker = EventTracker(
            lag_threshold=config_loader.get('alerts.lag_threshold', 1000)
        )
        
        self.monitoring_thread = None
        self.is_running = False
        self.current_groups = {}  # 当前监控的消费者组状态
        self.monitoring_stats = {
            'start_time': None,
            'total_cycles': 0,
            'last_update': None,
            'errors': 0
        }
        
        # 回调函数列表
        self.event_callbacks = []
        self.update_callbacks = []
    
    def start(self):
        """
        启动监控
        """
        if self.is_running:
            logger.warning("监控器已经在运行中")
            return
        
        try:
            # 初始化Kafka客户端
            kafka_config = self.config_loader.get_kafka_config()
            self.kafka_client = KafkaClient(kafka_config)
            
            # 启动监控线程
            self.is_running = True
            self.monitoring_stats['start_time'] = datetime.now()
            self.monitoring_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
            self.monitoring_thread.start()
            
            logger.info("消费者组监控器启动成功")
            
        except Exception as e:
            logger.error(f"启动监控器失败: {e}")
            self.is_running = False
            raise
    
    def stop(self):
        """
        停止监控
        """
        if not self.is_running:
            return
        
        logger.info("正在停止消费者组监控器...")
        self.is_running = False
        
        if self.monitoring_thread and self.monitoring_thread.is_alive():
            self.monitoring_thread.join(timeout=10)
        
        if self.kafka_client:
            self.kafka_client.close()
        
        logger.info("消费者组监控器已停止")
    
    def _monitoring_loop(self):
        """
        监控主循环
        """
        monitoring_interval = self.config_loader.get('monitoring.interval', 30)
        
        while self.is_running:
            try:
                cycle_start_time = time.time()
                
                # 执行一次监控周期
                self._monitor_cycle()
                
                # 更新统计信息
                self.monitoring_stats['total_cycles'] += 1
                self.monitoring_stats['last_update'] = datetime.now()
                
                # 计算休眠时间
                cycle_duration = time.time() - cycle_start_time
                sleep_time = max(0, monitoring_interval - cycle_duration)
                
                if sleep_time > 0:
                    time.sleep(sleep_time)
                else:
                    logger.warning(f"监控周期耗时 {cycle_duration:.2f}s 超过配置间隔 {monitoring_interval}s")
                
            except Exception as e:
                logger.error(f"监控周期执行失败: {e}")
                self.monitoring_stats['errors'] += 1
                time.sleep(5)  # 出错时短暂休眠
    
    def _monitor_cycle(self):
        """
        执行一次完整的监控周期
        """
        # 获取所有消费者组
        group_ids = self.kafka_client.get_consumer_groups()
        
        if not group_ids:
            logger.debug("未发现任何消费者组")
            return
        
        # 过滤需要监控的消费者组
        monitored_groups = [
            group_id for group_id in group_ids
            if self.config_loader.should_monitor_group(group_id)
        ]
        
        logger.debug(f"发现 {len(group_ids)} 个消费者组，监控 {len(monitored_groups)} 个")
        
        # 监控每个消费者组
        updated_groups = {}
        all_events = []
        
        for group_id in monitored_groups:
            try:
                # 获取消费者组信息
                consumer_group = self.kafka_client.get_consumer_group_info(group_id)
                
                if consumer_group is None:
                    continue
                
                # 过滤主题
                filtered_partitions = [
                    p for p in consumer_group.partitions
                    if self.config_loader.should_monitor_topic(p.topic)
                ]
                consumer_group.partitions = filtered_partitions
                
                # 计算积压信息
                lag_stats = self.lag_calculator.calculate_group_lag(consumer_group)
                
                # 跟踪事件
                events = self.event_tracker.track_consumer_group(consumer_group)
                all_events.extend(events)
                
                # 存储更新后的消费者组信息
                updated_groups[group_id] = {
                    'consumer_group': consumer_group,
                    'lag_stats': lag_stats,
                    'events': events
                }
                
            except Exception as e:
                logger.error(f"监控消费者组 {group_id} 失败: {e}")
        
        # 检测已删除的消费者组
        deleted_groups = set(self.current_groups.keys()) - set(updated_groups.keys())
        for group_id in deleted_groups:
            event = Event(
                event_type='consumer_group_deleted',
                group_id=group_id,
                message=f"消费者组 {group_id} 已删除或不再可见"
            )
            all_events.append(event)
        
        # 更新当前状态
        self.current_groups = updated_groups
        
        # 触发回调函数
        self._trigger_callbacks(all_events)
    
    def _trigger_callbacks(self, events: List[Event]):
        """
        触发回调函数
        
        参数:
            events: 事件列表
        """
        # 触发事件回调
        for event in events:
            for callback in self.event_callbacks:
                try:
                    callback(event)
                except Exception as e:
                    logger.error(f"事件回调执行失败: {e}")
        
        # 触发更新回调
        for callback in self.update_callbacks:
            try:
                callback(self.current_groups)
            except Exception as e:
                logger.error(f"更新回调执行失败: {e}")
    
    def add_event_callback(self, callback: Callable[[Event], None]):
        """
        添加事件回调函数
        
        参数:
            callback: 事件回调函数
        """
        self.event_callbacks.append(callback)
    
    def add_update_callback(self, callback: Callable[[Dict], None]):
        """
        添加更新回调函数
        
        参数:
            callback: 更新回调函数
        """
        self.update_callbacks.append(callback)
    
    def get_current_groups(self) -> Dict[str, Dict]:
        """
        获取当前监控的消费者组状态
        
        返回值:
            Dict[str, Dict]: 消费者组状态字典
        """
        return self.current_groups.copy()
    
    def get_group_info(self, group_id: str) -> Optional[Dict]:
        """
        获取指定消费者组的信息
        
        参数:
            group_id: 消费者组ID
            
        返回值:
            Optional[Dict]: 消费者组信息，不存在时返回None
        """
        return self.current_groups.get(group_id)
    
    def get_monitoring_stats(self) -> Dict:
        """
        获取监控统计信息
        
        返回值:
            Dict: 监控统计信息
        """
        stats = self.monitoring_stats.copy()
        
        if stats['start_time']:
            stats['uptime_seconds'] = (datetime.now() - stats['start_time']).total_seconds()
        
        stats['monitored_groups_count'] = len(self.current_groups)
        stats['is_running'] = self.is_running
        
        return stats
    
    def get_lag_trends(self, group_id: str, hours: int = 1) -> Dict:
        """
        获取消费者组的积压趋势
        
        参数:
            group_id: 消费者组ID
            hours: 分析时间范围（小时）
            
        返回值:
            Dict: 积压趋势信息
        """
        return self.lag_calculator.get_lag_trend(group_id, hours)
    
    def get_recent_events(self, group_id: Optional[str] = None, hours: int = 1) -> List[Event]:
        """
        获取最近的事件
        
        参数:
            group_id: 消费者组ID，为None时返回所有组的事件
            hours: 时间范围（小时）
            
        返回值:
            List[Event]: 事件列表
        """
        return self.event_tracker.get_recent_events(group_id, hours)
    
    def detect_lag_anomalies(self, group_id: str) -> List[Dict]:
        """
        检测积压异常
        
        参数:
            group_id: 消费者组ID
            
        返回值:
            List[Dict]: 异常列表
        """
        return self.lag_calculator.detect_lag_anomalies(group_id)
    
    def get_partition_lag_ranking(self, group_id: str, top_n: int = 10) -> List[Dict]:
        """
        获取分区积压排名
        
        参数:
            group_id: 消费者组ID
            top_n: 返回前N个分区
            
        返回值:
            List[Dict]: 分区积压排名
        """
        group_info = self.get_group_info(group_id)
        if not group_info:
            return []
        
        consumer_group = group_info['consumer_group']
        return self.lag_calculator.get_partition_lag_ranking(consumer_group, top_n)
    
    def __enter__(self):
        """
        上下文管理器入口
        """
        self.start()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """
        上下文管理器出口
        """
        self.stop()
