"""
消费者组数据模型

定义消费者组相关的数据结构，包括组状态、成员信息、分区分配等
"""

from dataclasses import dataclass, field
from typing import List, Dict, Optional
from datetime import datetime
from enum import Enum

from .partition_info import PartitionInfo


class ConsumerGroupState(Enum):
    """
    消费者组状态枚举
    """
    STABLE = "Stable"                           # 稳定状态
    PREPARING_REBALANCE = "PreparingRebalance"  # 准备重平衡
    COMPLETING_REBALANCE = "CompletingRebalance" # 完成重平衡
    DEAD = "Dead"                               # 死亡状态
    EMPTY = "Empty"                             # 空状态
    UNKNOWN = "Unknown"                         # 未知状态


@dataclass
class ConsumerMember:
    """
    消费者成员信息
    """
    member_id: str                      # 成员ID
    client_id: str                      # 客户端ID
    client_host: str                    # 客户端主机
    member_metadata: Optional[bytes]    # 成员元数据
    member_assignment: Optional[bytes]  # 成员分配信息
    
    def to_dict(self) -> dict:
        """
        转换为字典格式
        
        返回值:
            dict: 成员信息字典
        """
        return {
            'member_id': self.member_id,
            'client_id': self.client_id,
            'client_host': self.client_host,
            'has_metadata': self.member_metadata is not None,
            'has_assignment': self.member_assignment is not None
        }


@dataclass
class ConsumerGroup:
    """
    消费者组数据模型
    
    包含消费者组的完整状态信息，包括基本信息、成员列表、分区分配等
    """
    
    group_id: str                                   # 消费者组ID
    state: ConsumerGroupState                       # 消费者组状态
    protocol_type: Optional[str]                    # 协议类型
    protocol: Optional[str]                         # 协议名称
    members: List[ConsumerMember] = field(default_factory=list)  # 成员列表
    partitions: List[PartitionInfo] = field(default_factory=list)  # 分区信息列表
    coordinator_id: Optional[int] = None            # 协调器ID
    last_updated: datetime = field(default_factory=datetime.now)  # 最后更新时间
    
    @property
    def total_lag(self) -> int:
        """
        计算总积压数量
        
        返回值:
            int: 总积压数量
        """
        return sum(p.lag for p in self.partitions if p.lag is not None)
    
    @property
    def active_partitions(self) -> List[PartitionInfo]:
        """
        获取有活跃消费者的分区列表
        
        返回值:
            List[PartitionInfo]: 活跃分区列表
        """
        return [p for p in self.partitions if p.is_active]
    
    @property
    def lagging_partitions(self) -> List[PartitionInfo]:
        """
        获取有积压的分区列表
        
        返回值:
            List[PartitionInfo]: 有积压的分区列表
        """
        return [p for p in self.partitions if p.has_lag]
    
    @property
    def topics(self) -> List[str]:
        """
        获取消费者组订阅的主题列表
        
        返回值:
            List[str]: 主题列表
        """
        return list(set(p.topic for p in self.partitions))
    
    @property
    def member_count(self) -> int:
        """
        获取消费者成员数量
        
        返回值:
            int: 成员数量
        """
        return len(self.members)
    
    @property
    def is_stable(self) -> bool:
        """
        检查消费者组是否处于稳定状态
        
        返回值:
            bool: 是否稳定
        """
        return self.state == ConsumerGroupState.STABLE
    
    @property
    def is_rebalancing(self) -> bool:
        """
        检查消费者组是否正在重平衡
        
        返回值:
            bool: 是否正在重平衡
        """
        return self.state in [
            ConsumerGroupState.PREPARING_REBALANCE,
            ConsumerGroupState.COMPLETING_REBALANCE
        ]
    
    def get_partition_by_topic(self, topic: str) -> List[PartitionInfo]:
        """
        根据主题获取分区信息
        
        参数:
            topic: 主题名称
            
        返回值:
            List[PartitionInfo]: 指定主题的分区列表
        """
        return [p for p in self.partitions if p.topic == topic]
    
    def to_dict(self) -> dict:
        """
        转换为字典格式
        
        返回值:
            dict: 消费者组信息字典
        """
        return {
            'group_id': self.group_id,
            'state': self.state.value,
            'protocol_type': self.protocol_type,
            'protocol': self.protocol,
            'member_count': self.member_count,
            'total_lag': self.total_lag,
            'topics': self.topics,
            'coordinator_id': self.coordinator_id,
            'last_updated': self.last_updated.isoformat(),
            'is_stable': self.is_stable,
            'is_rebalancing': self.is_rebalancing,
            'members': [m.to_dict() for m in self.members],
            'partitions': [p.to_dict() for p in self.partitions]
        }
