"""
分区信息数据模型

定义Kafka分区相关的数据结构，包括分区状态、offset信息等
"""

from dataclasses import dataclass
from typing import Optional
from datetime import datetime


@dataclass
class PartitionInfo:
    """
    分区信息数据模型
    
    包含分区的基本信息、offset状态和lag计算结果
    """
    
    topic: str                          # 主题名称
    partition: int                      # 分区编号
    current_offset: Optional[int]       # 当前消费offset
    latest_offset: Optional[int]        # 最新可用offset
    lag: Optional[int]                  # 积压数量
    consumer_id: Optional[str]          # 消费者ID
    client_id: Optional[str]            # 客户端ID
    host: Optional[str]                 # 消费者主机
    last_updated: datetime              # 最后更新时间
    
    def __post_init__(self):
        """
        初始化后处理，计算lag值
        """
        if self.current_offset is not None and self.latest_offset is not None:
            self.lag = max(0, self.latest_offset - self.current_offset)
        else:
            self.lag = None
    
    @property
    def has_lag(self) -> bool:
        """
        检查是否存在积压
        
        返回值:
            bool: 是否存在积压
        """
        return self.lag is not None and self.lag > 0
    
    @property
    def is_active(self) -> bool:
        """
        检查分区是否有活跃的消费者
        
        返回值:
            bool: 是否有活跃消费者
        """
        return self.consumer_id is not None
    
    def to_dict(self) -> dict:
        """
        转换为字典格式
        
        返回值:
            dict: 分区信息字典
        """
        return {
            'topic': self.topic,
            'partition': self.partition,
            'current_offset': self.current_offset,
            'latest_offset': self.latest_offset,
            'lag': self.lag,
            'consumer_id': self.consumer_id,
            'client_id': self.client_id,
            'host': self.host,
            'last_updated': self.last_updated.isoformat() if self.last_updated else None,
            'has_lag': self.has_lag,
            'is_active': self.is_active
        }
