"""
事件数据模型

定义监控过程中产生的各种事件，包括状态变化、重平衡事件等
"""

from dataclasses import dataclass, field
from typing import Dict, Any, Optional, List
from datetime import datetime
from enum import Enum


class EventType(Enum):
    """
    事件类型枚举
    """
    GROUP_STATE_CHANGE = "group_state_change"       # 消费者组状态变化
    REBALANCE_START = "rebalance_start"             # 重平衡开始
    REBALANCE_COMPLETE = "rebalance_complete"       # 重平衡完成
    MEMBER_JOIN = "member_join"                     # 成员加入
    MEMBER_LEAVE = "member_leave"                   # 成员离开
    LAG_THRESHOLD_EXCEEDED = "lag_threshold_exceeded"  # 积压超过阈值
    PARTITION_ASSIGNMENT_CHANGE = "partition_assignment_change"  # 分区分配变化
    CONSUMER_GROUP_CREATED = "consumer_group_created"  # 消费者组创建
    CONSUMER_GROUP_DELETED = "consumer_group_deleted"  # 消费者组删除


class EventSeverity(Enum):
    """
    事件严重程度枚举
    """
    INFO = "info"           # 信息
    WARNING = "warning"     # 警告
    ERROR = "error"         # 错误
    CRITICAL = "critical"   # 严重


@dataclass
class Event:
    """
    监控事件数据模型
    
    记录监控过程中发生的各种事件，包括时间戳、类型、详细信息等
    """
    
    event_type: EventType                           # 事件类型
    group_id: str                                   # 消费者组ID
    timestamp: datetime = field(default_factory=datetime.now)  # 事件时间戳
    severity: EventSeverity = EventSeverity.INFO    # 事件严重程度
    message: str = ""                               # 事件消息
    details: Dict[str, Any] = field(default_factory=dict)  # 事件详细信息
    
    @property
    def event_id(self) -> str:
        """
        生成事件唯一标识
        
        返回值:
            str: 事件ID
        """
        return f"{self.group_id}_{self.event_type.value}_{self.timestamp.strftime('%Y%m%d_%H%M%S_%f')}"
    
    def to_dict(self) -> dict:
        """
        转换为字典格式
        
        返回值:
            dict: 事件信息字典
        """
        return {
            'event_id': self.event_id,
            'event_type': self.event_type.value,
            'group_id': self.group_id,
            'timestamp': self.timestamp.isoformat(),
            'severity': self.severity.value,
            'message': self.message,
            'details': self.details
        }


@dataclass
class RebalanceEvent(Event):
    """
    重平衡事件数据模型
    
    专门用于记录消费者组重平衡相关的事件信息
    """
    
    trigger_reason: Optional[str] = None            # 触发原因
    participating_members: List[str] = field(default_factory=list)  # 参与重平衡的成员
    partition_reassignments: Dict[str, List[int]] = field(default_factory=dict)  # 分区重新分配
    duration_ms: Optional[int] = None               # 重平衡持续时间（毫秒）
    
    def __post_init__(self):
        """
        初始化后处理
        """
        if self.event_type not in [EventType.REBALANCE_START, EventType.REBALANCE_COMPLETE]:
            self.event_type = EventType.REBALANCE_START
    
    def to_dict(self) -> dict:
        """
        转换为字典格式，包含重平衡特有信息
        
        返回值:
            dict: 重平衡事件信息字典
        """
        base_dict = super().to_dict()
        base_dict.update({
            'trigger_reason': self.trigger_reason,
            'participating_members': self.participating_members,
            'partition_reassignments': self.partition_reassignments,
            'duration_ms': self.duration_ms
        })
        return base_dict


@dataclass
class LagEvent(Event):
    """
    积压事件数据模型
    
    专门用于记录消费者组积压相关的事件信息
    """
    
    total_lag: int = 0                              # 总积压数量
    threshold: int = 0                              # 告警阈值
    affected_topics: List[str] = field(default_factory=list)  # 受影响的主题
    partition_lags: Dict[str, int] = field(default_factory=dict)  # 各分区积压情况
    
    def __post_init__(self):
        """
        初始化后处理
        """
        if self.event_type != EventType.LAG_THRESHOLD_EXCEEDED:
            self.event_type = EventType.LAG_THRESHOLD_EXCEEDED
        
        if self.total_lag > self.threshold:
            self.severity = EventSeverity.WARNING if self.total_lag < self.threshold * 2 else EventSeverity.ERROR
    
    def to_dict(self) -> dict:
        """
        转换为字典格式，包含积压特有信息
        
        返回值:
            dict: 积压事件信息字典
        """
        base_dict = super().to_dict()
        base_dict.update({
            'total_lag': self.total_lag,
            'threshold': self.threshold,
            'affected_topics': self.affected_topics,
            'partition_lags': self.partition_lags
        })
        return base_dict
