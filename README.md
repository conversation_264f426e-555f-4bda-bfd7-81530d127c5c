# PythonProject - Kafka工具集合

本项目包含两个独立的Kafka相关工具程序，每个都有完整的功能和独立的运行环境。

## 项目概览

### 🔍 Kafka消费者组监控程序
**目录**: `kafka-consumer-monitor/`

一个功能完整的Kafka消费者组监控工具，提供实时监控、事件跟踪、告警通知和报告生成等功能。

**主要功能**:
- 实时监控Kafka集群中的消费者组状态和积压情况
- 积压分析和趋势分析
- 事件跟踪，特别关注重平衡（rebalance）事件
- 多种输出方式：控制台、文件、告警

**快速开始**:
```bash
cd kafka-consumer-monitor
pip install -r requirements.txt
python main.py --config config.example.yaml
```

### 🔄 Kafka数据过滤程序
**目录**: `kafka-data-filter/`

一个强大的Kafka数据过滤程序，支持从指定的Kafka topic中消费JSON格式的消息数据，根据用户定义的复杂过滤规则筛选符合条件的数据。

**主要功能**:
- 从Kafka topic消费JSON格式消息
- 支持复杂的多层嵌套字段访问（如：字段2.class2）
- 完整的过滤规则支持（逻辑运算符、比较运算符、括号分组）
- 实时统计和性能监控
- 多种输出格式和方式

**快速开始**:
```bash
cd kafka-data-filter
pip install -r requirements.txt
python main.py example_config.yaml
```

## 项目特点

### ✅ 完全独立
- 每个项目有独立的依赖管理（requirements.txt）
- 独立的配置文件和文档
- 独立的模块命名空间，避免冲突
- 可以单独部署和运行

### 🛠️ 技术栈
- **共同依赖**: kafka-python-ng, PyYAML
- **项目1额外依赖**: colorama, tabulate, click
- **Python版本**: 3.7+

### 📁 目录结构
```
PythonProject/
├── kafka-consumer-monitor/     # Kafka消费者组监控程序
│   ├── README.md              # 项目1专用文档
│   ├── requirements.txt       # 项目1依赖
│   ├── main.py               # 项目1主入口
│   └── kafka_consumer_monitor/ # 项目1核心模块
├── kafka-data-filter/         # Kafka数据过滤程序
│   ├── README.md              # 项目2专用文档
│   ├── requirements.txt       # 项目2依赖
│   ├── main.py               # 项目2主入口
│   └── kafka_data_filter/     # 项目2核心模块
└── docs/                      # 共享文档
    ├── PROJECT_OVERVIEW.md    # 项目总览
    └── DEVELOPMENT_GUIDE.md   # 开发指南
```

## 使用指南

### 选择合适的工具

**使用Kafka消费者组监控程序，如果您需要**:
- 监控Kafka集群的健康状态
- 跟踪消费者组的积压情况
- 分析消费者组的重平衡事件
- 生成监控报告和告警

**使用Kafka数据过滤程序，如果您需要**:
- 从Kafka消息中筛选特定数据
- 应用复杂的过滤规则
- 处理JSON格式的消息数据
- 实时数据处理和转换

### 环境要求

- Python 3.7+
- 可访问的Kafka集群
- 根据项目需求安装相应依赖

## 快速开始

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd PythonProject
   ```

2. **选择并进入项目目录**
   ```bash
   # 选择监控程序
   cd kafka-consumer-monitor
   
   # 或选择过滤程序
   cd kafka-data-filter
   ```

3. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

4. **配置和运行**
   - 复制并修改配置文件
   - 运行相应的主程序

## 文档

- **项目1文档**: `kafka-consumer-monitor/README.md`
- **项目2文档**: `kafka-data-filter/README.md`
- **开发指南**: `docs/DEVELOPMENT_GUIDE.md`
- **项目总览**: `docs/PROJECT_OVERVIEW.md`

## 贡献

欢迎贡献代码和建议！请查看各项目的README文档了解具体的贡献指南。

## 许可证

本项目遵循MIT许可证。

## 支持

如有问题或需要帮助，请：
1. 查看相应项目的README文档
2. 查看开发指南
3. 提交Issue或联系维护者
