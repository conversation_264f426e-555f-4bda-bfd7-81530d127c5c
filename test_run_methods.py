#!/usr/bin/env python3
"""
测试两种运行方式的脚本

验证直接运行main.py和使用启动脚本两种方式都能正常工作
"""

import subprocess
import sys
import os


def test_main_py_help():
    """测试直接运行main.py的帮助信息"""
    print("测试: python kafka_data_filter/main.py --help")
    try:
        result = subprocess.run([
            sys.executable, "kafka_data_filter/main.py", "--help"
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("✅ 直接运行main.py --help 成功")
            return True
        else:
            print(f"❌ 直接运行main.py --help 失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


def test_main_py_version():
    """测试直接运行main.py的版本信息"""
    print("测试: python kafka_data_filter/main.py --version")
    try:
        result = subprocess.run([
            sys.executable, "kafka_data_filter/main.py", "--version"
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0 and "v1.0.0" in result.stdout:
            print("✅ 直接运行main.py --version 成功")
            return True
        else:
            print(f"❌ 直接运行main.py --version 失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


def test_startup_script_help():
    """测试启动脚本的帮助信息"""
    print("测试: python kafka_filter_main.py --help")
    try:
        result = subprocess.run([
            sys.executable, "kafka_filter_main.py", "--help"
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("✅ 启动脚本 --help 成功")
            return True
        else:
            print(f"❌ 启动脚本 --help 失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


def test_config_validation():
    """测试配置文件验证"""
    print("测试: 配置文件不存在的错误处理")
    try:
        result = subprocess.run([
            sys.executable, "kafka_data_filter/main.py", "nonexistent.yaml"
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 1 and "配置文件不存在" in result.stdout:
            print("✅ 配置文件验证成功")
            return True
        else:
            print(f"❌ 配置文件验证失败: {result.stdout}")
            return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


def main():
    """运行所有测试"""
    print("=" * 60)
    print("Kafka数据过滤程序运行方式测试")
    print("=" * 60)
    
    tests = [
        test_main_py_help,
        test_main_py_version,
        test_startup_script_help,
        test_config_validation
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        print()
        if test():
            passed += 1
        print("-" * 40)
    
    print()
    print("=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    print("=" * 60)
    
    if passed == total:
        print("🎉 所有测试通过！两种运行方式都正常工作。")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关功能。")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
